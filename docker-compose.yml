version: '3.8'
services:
  db:
    image: mysql:8.0
    command: '--default-authentication-plugin=mysql_native_password'
    volumes:
      - mysql-data:/var/lib/mysql
      - ./sqldump/0-seed.sql:/docker-entrypoint-initdb.d/0-seed.sql
      - ../2025-07-04-chungwa.sql.gz:/docker-entrypoint-initdb.d/data.sql.gz
    networks:
      - my_app_network
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: chungwa_development
    ports:
      - '3306:3306'
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD}"]
      interval: 10s   # 每 10 秒檢查一次
      timeout: 5s     # 每次檢查嘅超時時間
      retries: 999
  web:
    build: .
    command: bash -c "rm -f tmp/pids/server.pid && bundle exec rails s -p 3000 -b '0.0.0.0'"
    volumes:
      - .:/myapp
    ports:
      - "3000:3000"
    restart: unless-stopped
    depends_on:
      db:
        condition: service_healthy
    networks:
      - my_app_network
    environment:
      DATABASE_HOST: db
      DATABASE_USER: root
      DATABASE_PASSWORD: password
      RAILS_ENV: development

volumes:
  mysql-data:

networks:
  my_app_network:
    driver: bridge