GEM
  remote: https://rubygems.org/
  specs:
    actionmailer (********)
      actionpack (= ********)
      actionview (= ********)
      activejob (= ********)
      mail (~> 2.5, >= 2.5.4)
      rails-dom-testing (~> 1.0, >= 1.0.5)
    actionpack (********)
      actionview (= ********)
      activesupport (= ********)
      rack (~> 1.6)
      rack-test (~> 0.6.2)
      rails-dom-testing (~> 1.0, >= 1.0.5)
      rails-html-sanitizer (~> 1.0, >= 1.0.2)
    actionview (********)
      activesupport (= ********)
      builder (~> 3.1)
      erubis (~> 2.7.0)
      rails-dom-testing (~> 1.0, >= 1.0.5)
      rails-html-sanitizer (~> 1.0, >= 1.0.3)
    activeadmin (1.4.3)
      arbre (>= 1.1.1)
      coffee-rails
      formtastic (~> 3.1)
      formtastic_i18n
      inherited_resources (>= 1.9.0)
      jquery-rails (>= 4.2.0)
      kaminari (>= 0.15)
      railties (>= 4.2, < 5.3)
      ransack (>= 1.8.7)
      sass (~> 3.1)
      sprockets (< 4.1)
    activejob (********)
      activesupport (= ********)
      globalid (>= 0.3.0)
    activemodel (********)
      activesupport (= ********)
      builder (~> 3.1)
    activerecord (********)
      activemodel (= ********)
      activesupport (= ********)
      arel (~> 6.0)
    activeresource (4.1.0)
      activemodel (~> 4.0)
      activesupport (~> 4.0)
      rails-observers (~> 0.1.2)
    activesupport (********)
      i18n (~> 0.7)
      minitest (~> 5.1)
      thread_safe (~> 0.3, >= 0.3.4)
      tzinfo (~> 1.1)
    arbre (1.2.1)
      activesupport (>= 3.0.0)
    arel (6.0.4)
    aws-sdk-v1 (1.67.0)
      json (~> 1.4)
      nokogiri (~> 1)
    bcrypt (3.1.13)
    builder (3.2.4)
    cancan (1.6.10)
    carrierwave (1.3.1)
      activemodel (>= 4.0.0)
      activesupport (>= 4.0.0)
      mime-types (>= 1.16)
    chronic (0.10.2)
    chunky_png (1.3.11)
    climate_control (0.2.0)
    cocaine (0.5.8)
      climate_control (>= 0.0.3, < 1.0)
    coderay (1.1.2)
    coffee-rails (4.2.2)
      coffee-script (>= 2.2.0)
      railties (>= 4.0.0)
    coffee-script (2.4.1)
      coffee-script-source
      execjs
    coffee-script-source (1.12.2)
    compass (0.12.7)
      chunky_png (~> 1.2)
      fssm (>= 0.2.7)
      sass (~> 3.2.19)
    compass-rails (2.0.0)
      compass (>= 0.12.2)
    concurrent-ruby (1.1.5)
    crass (1.0.6)
    devise (4.7.1)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    diff-lcs (1.3)
    differ (0.1.2)
    domain_name (0.5.20190701)
      unf (>= 0.0.5, < 1.0.0)
    enumerize (2.3.1)
      activesupport (>= 3.2)
    erubis (2.7.0)
    exception_notification (4.4.0)
      actionmailer (>= 4.0, < 7)
      activesupport (>= 4.0, < 7)
    execjs (2.7.0)
    ffi (1.15.4)
    formatador (0.2.5)
    formtastic (3.1.5)
      actionpack (>= 3.2.13)
    formtastic_i18n (0.6.0)
    friendly_id (5.2.5)
      activerecord (>= 4.0.0)
    fssm (0.2.10)
    globalid (0.4.2)
      activesupport (>= 4.2.0)
    guard (2.16.1)
      formatador (>= 0.2.4)
      listen (>= 2.7, < 4.0)
      lumberjack (>= 1.0.12, < 2.0)
      nenv (~> 0.1)
      notiffany (~> 0.0)
      pry (>= 0.9.12)
      shellany (~> 0.0)
      thor (>= 0.18.1)
    haml (4.0.7)
      tilt
    haml-rails (0.9.0)
      actionpack (>= 4.0.1)
      activesupport (>= 4.0.1)
      haml (>= 4.0.6, < 5.0)
      html2haml (>= 1.0.1)
      railties (>= 4.0.1)
    has_scope (0.7.2)
      actionpack (>= 4.1)
      activesupport (>= 4.1)
    hike (1.2.3)
    html2haml (2.2.0)
      erubis (~> 2.7.0)
      haml (>= 4.0, < 6)
      nokogiri (>= 1.6.0)
      ruby_parser (~> 3.5)
    http-cookie (1.0.3)
      domain_name (~> 0.5)
    httparty (0.17.3)
      mime-types (~> 3.0)
      multi_xml (>= 0.5.2)
    i18n (0.9.5)
      concurrent-ruby (~> 1.0)
    inherited_resources (1.9.0)
      actionpack (>= 4.2, < 5.3)
      has_scope (~> 0.6)
      railties (>= 4.2, < 5.3)
      responders
    jbuilder (2.9.1)
      activesupport (>= 4.2.0)
    jquery-rails (4.3.5)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    json (1.8.6)
    jwt (2.2.1)
    kaminari (1.1.1)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.1.1)
      kaminari-activerecord (= 1.1.1)
      kaminari-core (= 1.1.1)
    kaminari-actionview (1.1.1)
      actionview
      kaminari-core (= 1.1.1)
    kaminari-activerecord (1.1.1)
      activerecord
      kaminari-core (= 1.1.1)
    kaminari-core (1.1.1)
    listen (3.1.5)
      rb-fsevent (~> 0.9, >= 0.9.4)
      rb-inotify (~> 0.9, >= 0.9.7)
      ruby_dep (~> 1.2)
    loofah (2.4.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.5.9)
    lumberjack (1.0.13)
    mail (2.7.1)
      mini_mime (>= 0.1.1)
    mailgun-ruby (1.1.11)
      rest-client (~> 2.0.2)
    method_source (0.9.2)
    mime-types (3.3.1)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2019.1009)
    mini_mime (1.0.2)
    mini_portile2 (2.5.3)
    minitest (5.14.0)
    modular-scale (1.0.6)
      compass (>= 0.12.1)
      sass (>= 3.2.0)
      sassy-math (>= 1.5)
    multi_json (1.14.1)
    multi_xml (0.6.0)
    mysql2 (0.5.6)
    nenv (0.3.0)
    netrc (0.11.0)
    nexmo (6.1.0)
      jwt (~> 2)
      zeitwerk (~> 2)
    nokogiri (1.11.7)
      mini_portile2 (~> 2.5.0)
      racc (~> 1.4)
    notiffany (0.1.3)
      nenv (~> 0.1)
      shellany (~> 0.0)
    orm_adapter (0.5.0)
    pacecar (2.0.0)
      activerecord (>= 4.0.0)
    paper_trail (10.3.1)
      activerecord (>= 4.2)
      request_store (~> 1.1)
    paperclip (4.2.4)
      activemodel (>= 3.2.0)
      activesupport (>= 3.2.0)
      cocaine (~> 0.5.5)
      mime-types
    protected_attributes (1.1.4)
      activemodel (>= 4.0.1, < 5.0)
    pry (0.12.2)
      coderay (~> 1.1.0)
      method_source (~> 0.9.0)
    racc (1.8.1)
    rack (1.6.12)
    rack-test (0.6.3)
      rack (>= 1.0)
    rails (********)
      actionmailer (= ********)
      actionpack (= ********)
      actionview (= ********)
      activejob (= ********)
      activemodel (= ********)
      activerecord (= ********)
      activesupport (= ********)
      bundler (>= 1.3.0, < 2.0)
      railties (= ********)
      sprockets-rails
    rails-deprecated_sanitizer (1.0.3)
      activesupport (>= 4.2.0.alpha)
    rails-dom-testing (1.0.9)
      activesupport (>= 4.2.0, < 5.0)
      nokogiri (~> 1.6)
      rails-deprecated_sanitizer (>= 1.0.1)
    rails-html-sanitizer (1.3.0)
      loofah (~> 2.3)
    rails-observers (0.1.5)
      activemodel (>= 4.0)
    railties (********)
      actionpack (= ********)
      activesupport (= ********)
      rake (>= 0.8.7)
      thor (>= 0.18.1, < 2.0)
    rake (13.0.1)
    ransack (1.8.10)
      actionpack (>= 3.0, < 5.2)
      activerecord (>= 3.0, < 5.2)
      activesupport (>= 3.0, < 5.2)
      i18n
    rb-fsevent (0.9.8)
    rb-inotify (0.10.0)
      ffi (~> 1.0)
    request_store (1.5.0)
      rack (>= 1.4)
    responders (2.4.1)
      actionpack (>= 4.2.0, < 6.0)
      railties (>= 4.2.0, < 6.0)
    rest-client (2.0.2)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    roo (2.8.2)
      nokogiri (~> 1)
      rubyzip (>= 1.2.1, < 2.0.0)
    rspec-core (3.9.1)
      rspec-support (~> 3.9.1)
    rspec-expectations (3.9.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.9.0)
    rspec-mocks (3.9.1)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.9.0)
    rspec-rails (3.9.0)
      actionpack (>= 3.0)
      activesupport (>= 3.0)
      railties (>= 3.0)
      rspec-core (~> 3.9.0)
      rspec-expectations (~> 3.9.0)
      rspec-mocks (~> 3.9.0)
      rspec-support (~> 3.9.0)
    rspec-support (3.9.2)
    ruby_dep (1.5.0)
    ruby_parser (3.14.1)
      sexp_processor (~> 4.9)
    rubyzip (1.3.0)
    sass (3.2.19)
    sass-rails (4.0.5)
      railties (>= 4.0.0, < 5.0)
      sass (~> 3.2.2)
      sprockets (~> 2.8, < 3.0)
      sprockets-rails (~> 2.0)
    sassy-math (1.5.1)
      compass (>= 0.11)
    sexp_processor (4.13.0)
    shellany (0.0.1)
    simple_calendar (0.1.11)
      rails (>= 3.0)
    simple_form (4.0.0)
      actionpack (> 4)
      activemodel (> 4)
    sprockets (2.12.5)
      hike (~> 1.2)
      multi_json (~> 1.0)
      rack (~> 1.0)
      tilt (~> 1.1, != 1.3.0)
    sprockets-rails (2.3.3)
      actionpack (>= 3.0)
      activesupport (>= 3.0)
      sprockets (>= 2.8, < 4.0)
    sqlite3 (1.3.13)
    table_for_collection (1.0.7)
      actionpack
    thor (1.0.1)
    thread_safe (0.3.6)
    tilt (1.4.1)
    traco (5.0.0)
      activerecord (>= 3.0)
    turbolinks (5.2.1)
      turbolinks-source (~> 5.2)
    turbolinks-source (5.2.0)
    tzinfo (1.2.6)
      thread_safe (~> 0.1)
    uglifier (4.2.0)
      execjs (>= 0.3.0, < 3)
    unf (0.1.4)
      unf_ext
    unf_ext (0.0.7.6)
    warden (1.2.7)
      rack (>= 1.0)
    whenever (1.0.0)
      chronic (>= 0.6.3)
    where-or (0.1.6)
    will_paginate (3.2.1)
    zeitwerk (2.2.1)
    zurb-foundation (3.2.5)
      compass (>= 0.12.2)
      modular-scale (= 1.0.6)
      rake
      sass (>= 3.2.0)

PLATFORMS
  ruby

DEPENDENCIES
  activeadmin
  activeresource
  aws-sdk-v1
  cancan
  carrierwave
  coffee-rails
  compass-rails
  devise
  differ
  enumerize
  exception_notification
  ffi (= 1.15.4)
  friendly_id (~> 5.2.4)
  guard
  haml-rails (~> 0.5)
  httparty
  jbuilder
  jquery-rails
  kaminari
  mailgun-ruby (~> 1.1.6)
  mysql2 (= 0.5.6)
  nexmo
  nokogiri (= 1.11.7)
  pacecar
  paper_trail
  paperclip (~> 4.0)
  protected_attributes
  rails (~> 4.2.11)
  ransack
  rb-fsevent (~> 0.9.1)
  roo
  rspec-rails (~> 3.5)
  sass-rails (~> 4.0.5)
  simple_calendar (~> 0.1.4)
  simple_form
  sqlite3 (~> 1.3.6)
  table_for_collection
  traco
  turbolinks
  uglifier
  whenever
  where-or (~> 0.1.6)
  will_paginate (~> 3.0)
  zurb-foundation (~> 3.2.5)

BUNDLED WITH
   1.17.3
