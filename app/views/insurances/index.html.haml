%h1 保險清單

= paginate @insurance_certs
%table
  %tr
    %th 保單編號
    %th 學生編號
    %th 證件號碼
    %th 中文名
    %th 英文名
    %th 學校
    %th 建立日期
    %th 有效日期
    %th 考試日期

  - @insurance_certs.each do |insurance_cert|
    - if insurance_cert.insurance.nil?
      - next
    %tr
      %td= insurance_cert.cert_no
      %td= insurance_cert.insurance.student.code
      %td= insurance_cert.insurance.student.id_card
      %td= link_to insurance_cert.insurance.student.chinese_name, insurance_cert.insurance.student
      %td= insurance_cert.insurance.student.eng_name
      %td= insurance_cert.insurance.student.school.name_zh
      %td
        - if insurance_cert.try(:start_at)
          = l insurance_cert.start_at, format: :date
      %td
        - if insurance_cert.try(:end_at)
          = l insurance_cert.end_at, format: :date
      %td
        - if insurance_cert.earlist_exam_at
          = l insurance_cert.earlist_exam_at, format: :long24
      / # %td= link_to 'Show', insurance
      / # %td= link_to 'Edit', edit_insurance_path(insurance)
      / # %td= link_to 'Destroy', insurance, method: :delete, data: { confirm: 'Are you sure?' }

= paginate @insurance_certs