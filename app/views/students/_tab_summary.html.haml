.row
  .twelve.columns
    %table.twelve.info-table
      %tr
        %th 學生編號
        %td.green= @student.code
      %tr
        %th 證件編號
        %td.blue
          - if @student.id_card
            = @student.id_card_kind.upcase
          = @student.id_card
      %tr
        %th 簽發資料
        %td
          %span.red= @student.issue_date
          由
          - if @student.issue_place
            %span.light-blue= @student.issue_place.upcase
          發出
      %tr
        %th 有效日期
        %td.orange
          = @student.id_card_valid_till
          - if @student.id_card_expired
            .alert.label 已過期
          - elsif @student.id_card_going_to_expire
            .label 即將過期
      %tr
        %th 姓名
        %td= "#{@student.chinese_name} #{@student.last_name}, #{@student.first_name}"
      %tr
        %th 性別
        %td= @student.gender_zh
      %tr
        %th 出生資料
        %td
          %span.red= @student.birthday
          於
          - if @student.birth_place
            %span.light-blue= @student.birth_place.upcase
          出生
          - if @student.non_adult
            .alert.label 未滿18歲
      %tr
        %th 國籍
        %td.normal-weight= @student.national
      %tr
        %th 語言
        %td= @student.language
      %tr
        %th 電話
        %td= @student.tel
      %tr
        %th 其他電話
        %td= @student.other_tel
      %tr
        %th 電郵地址
        %td= @student.email
      / - if !@student.fax.blank?
      /   %tr
      /     %th 傳真機號碼
      /     %td= @student.fax
      %tr
        %th 街道編號
        %td.normal-weight= @student.street_no
      %tr
        %th 葡文地址
        %td.normal-weight
          = @student.address
          = @student.address2
      %tr
        %th 中文地址
        %td.normal-weight= @student.chinese_address
      %tr
        %th 職業
        %td.normal-weight= @student.occupation
      %tr
        %th 視力
        %td= @student.vision_text
      %tr
        %th 介紹人
        %td.normal-weight= @student.referrer
    = render partial: "exams_table", locals: { show_remark: false, allow_edit: false}
    - if @student.licenses.size > 0
      %table.twelve.info-licenses-table
        %thead
          %tr
            %th 編號
            %th 類型
            %th 持有類別
            %th 考試日期
            %th 有效期至
        %tbody
        - @licenses.each do |license|
          - if license.allow_cicl
            =render partial: 'licenses/license_category_summary_row', locals: {license: license, category: 'CICL', category_exam_at: license.cicl_exam_at}
          - if license.allow_a1
            =render partial: 'licenses/license_category_summary_row', locals: {license: license, category: 'A1', category_exam_at: license.a1_exam_at}
          - if license.allow_a1_125
            =render partial: 'licenses/license_category_summary_row', locals: {license: license, category: 'A1-125', category_exam_at: license.a1_125_exam_at}
          - if license.allow_a1_150
            =render partial: 'licenses/license_category_summary_row', locals: {license: license, category: 'A1-150', category_exam_at: license.a1_150_exam_at}
          - if license.allow_a1_170
            =render partial: 'licenses/license_category_summary_row', locals: {license: license, category: 'A1-170', category_exam_at: license.a1_170_exam_at}
          - if license.allow_a1_200
            =render partial: 'licenses/license_category_summary_row', locals: {license: license, category: 'A1-200', category_exam_at: license.a1_200_exam_at}
          - if license.allow_a1_250
            =render partial: 'licenses/license_category_summary_row', locals: {license: license, category: 'A1-230', category_exam_at: license.a1_250_exam_at}
          - if license.allow_a2
            =render partial: 'licenses/license_category_summary_row', locals: {license: license, category: 'A2', category_exam_at: license.a2_exam_at}
          - if license.allow_b
            =render partial: 'licenses/license_category_summary_row', locals: {license: license, category: 'B', category_exam_at: license.b_exam_at}
          - if license.allow_b_auto
            =render partial: 'licenses/license_category_summary_row', locals: {license: license, category: 'B-Auto', category_exam_at: license.b_auto_exam_at}
          - if license.allow_c
            =render partial: 'licenses/license_category_summary_row', locals: {license: license, category: 'C', category_exam_at: license.c_exam_at}
          - if license.allow_d1
            =render partial: 'licenses/license_category_summary_row', locals: {license: license, category: 'D1', category_exam_at: license.d1_exam_at}
          - if license.allow_d2
            =render partial: 'licenses/license_category_summary_row', locals: {license: license, category: 'D2', category_exam_at: license.d2_exam_at}
          - if license.allow_e
            =render partial: 'licenses/license_category_summary_row', locals: {license: license, category: 'E', category_exam_at: license.e_exam_at}
    - if @student.receipts.length == 0
      .foundicon-idea.secondary.alert-box 未建立任何收據
    - else
      %table.twelve.info-receipts-table
        %thead
          %tr
            %th 學校
            %th 編號
            %th 付款方式
            %th 總數
            %th 建立日期
            %th 職員
        %tbody
        - @receipts.each do |receipt|
          %tr
            %td= receipt.school.name_zh
            %td
              = receipt.no
              - if receipt.is_void
                %span VOID
            %td= receipt.payment_type_text_or_name
            %td= receipt.total_price
            %td= l receipt.created_at, format: :date
            %td= receipt.staff.name_zh
    %strong 備註：
    - if @student.remark
      %p= @student.remark.gsub(/\n/, "<br>").html_safe
    / %ul.small.remarks
    /   - @student.remarks.each do |remark|
    /     - next if remark.deleted || remark.striked
    /     %li
    /       %span.secondary
    /         = l remark.created_at, format: :date
    /         = remark.staff.name_zh
    /       = ':'
    /       = remark.content
