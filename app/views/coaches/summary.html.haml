%p#notice= notice

%h1= @coach.name_zh

%p
  %b Coach no:
  = @coach.coach_no

%p
  %b School:
  = @coach.school.formal_name

%p
  %b Tel:
  = @coach.tel

= link_to 'Edit', edit_coach_path(@coach)
|
= link_to 'Back', coaches_path

%hr/


%div
  %form{action:"/coaches/#{@coach.id}/summary", method:'get',id:'date-picker',class:'hide-in-print'}
    %select{id:'year',name:'year'}
      = options_for_select (2010..Date.current.year+3), @year
    %select{id:'month',name:'month'}
      = options_for_select [1,2,3,4,5,6,7,8,9,10,11,12], @month
    %input{type: 'submit', value: '跳轉到日期'}

%h2
  選擇年月：
  = @year
  \-
  = @month

%table
  %thead
    %tr
      %td 學生姓名
      %td 學生姓名（外）
      %td 區域
      %td 首考
      %td 重考
      %td 類別
      %td 考期日期
      %td 時數
      %td 次數
      %td 結果
      %td 學費總額
      %td 薪資 教練
      %td 薪資 學校
      %td{:style => "width:25px"} 特
  %tbody
    - @road_exams.each do |exam|
      - cache exam do
        %tr.blue
          %td= exam.student.chinese_name
          %td= exam.student.eng_name
          %td= exam.student.school.single_letter
          %td= "1" if exam.application_form.is_failed == false
          %td= "1" if exam.application_form.is_failed # TODO-2025-08 要檢查埋過往有沒有同類 road_exam，及是否為 failed.
          %td= exam.application_form.category.description_for_summary
          %td= exam.latest_exam_at.to_date
          %td= exam.practiced_hours_or_fetched
          %td= exam.examed_times_or_fetched
          %td= exam.status_pass_or_fail
          %td= exam.total_tution_fee
          %td= exam.coach_fee
          %td= exam.school_fee
          %td= exam.application_form.license_kind.exam_record_text
