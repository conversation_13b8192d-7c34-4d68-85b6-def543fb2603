class LicenseKindsController < ApplicationController
  # GET /license_kinds
  # GET /license_kinds.json
  def index
    @license_kinds = LicenseKind.all

    respond_to do |format|
      format.html # index.html.haml
      format.json { render json: @license_kinds }
    end
  end

  # GET /license_kinds/1
  # GET /license_kinds/1.json
  def show
    @license_kind = LicenseKind.find(params[:id])

    respond_to do |format|
      format.html # show.html.haml
      format.json { render json: @license_kind }
    end
  end

  # GET /license_kinds/new
  # GET /license_kinds/new.json
  def new
    @license_kind = LicenseKind.new

    respond_to do |format|
      format.html # new.html.haml
      format.json { render json: @license_kind }
    end
  end

  # GET /license_kinds/1/edit
  def edit
    @license_kind = LicenseKind.find(params[:id])
  end

  # POST /license_kinds
  # POST /license_kinds.json
  def create
    @license_kind = LicenseKind.new(params[:license_kind])

    respond_to do |format|
      if @license_kind.save
        format.html { redirect_to @license_kind, notice: 'License kind was successfully created.' }
        format.json { render json: @license_kind, status: :created, location: @license_kind }
      else
        format.html { render action: "new" }
        format.json { render json: @license_kind.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /license_kinds/1
  # PUT /license_kinds/1.json
  def update
    @license_kind = LicenseKind.find(params[:id])

    respond_to do |format|
      if @license_kind.update_attributes(params[:license_kind])
        format.html { redirect_to @license_kind, notice: 'License kind was successfully updated.' }
        format.json { head :no_content }
      else
        format.html { render action: "edit" }
        format.json { render json: @license_kind.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /license_kinds/1
  # DELETE /license_kinds/1.json
  def destroy
    @license_kind = LicenseKind.find(params[:id])
    @license_kind.destroy

    respond_to do |format|
      format.html { redirect_to license_kinds_url }
      format.json { head :no_content }
    end
  end
end
