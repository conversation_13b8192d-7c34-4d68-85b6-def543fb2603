class ReceiptsController < ApplicationController

  before_filter :find_student_from_params_id

  # GET /receipts
  # GET /receipts.json
  def index
    @receipts = @student.receipts

    respond_to do |format|
      format.html # index.html.haml
      format.json { render json: @receipts }
    end
  end

  # GET /receipts/1
  # GET /receipts/1.json
  def show
    @receipt = Receipt.find(params[:id])

    respond_to do |format|
      format.html # show.html.haml
      format.json { render json: @receipt }
    end
  end



  def preview
    @receipt = Receipt.find(params[:id])
    respond_to do |format|
      format.html { render :layout => false }
      format.json { render json: @receipt }
    end
  end

  def print
    @receipt = Receipt.find(params[:id])
    respond_to do |format|
      format.html { render :layout => false}
      format.json { render json: @receipt }
    end
  end



  # GET /receipts/new
  # GET /receipts/new.json
  def new
    @receipt = Receipt.new

    @items = TemplateItem.order(:rank)

    @templates = Template.all

    @payment_type = PaymentType.where(name: "_").first
    @receipt.payment_type = @payment_type

    respond_to do |format|
      format.html # new.html.haml
      format.json { render json: @receipt }
    end
  end

  # GET /receipts/1/edit
  def edit
    @receipt = Receipt.find(params[:id])
  end

  # POST /receipts
  # POST /receipts.json
  def create
    @receipt = Receipt.new(params[:receipt])
    @receipt.student = @student
    @receipt.staff = current_staff
    @receipt.school = @student.school

    @receipt.no = ReceiptCode.next_code! @receipt.school_id

    # raise params.to_yaml
    params[:item].each do |key, value|
      # raise key.to_yaml
      # raise value.to_yaml
      # raise value[:description].to_yaml
      item = ReceiptItem.new
      item.description = value[:description]
      item.amount = value[:amount]
      item.price = value[:price]
      item.suggest_expense = value[:expense]
      item.receipt = @receipt
      item.save
    end

    respond_to do |format|
      if @receipt.save
        format.html { redirect_to [@student, @receipt], notice: 'Receipt was successfully created.' }
        format.json { render json: @receipt, status: :created, location: @receipt }
      else
        format.html { render action: "new" }
        format.json { render json: @receipt.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /receipts/1
  # PUT /receipts/1.json
  def update
    @receipt = Receipt.find(params[:id])

    respond_to do |format|
      if @receipt.update_attributes(params[:receipt])
        format.html { redirect_to [@student, @receipt], notice: 'Receipt was successfully updated.' }
        format.json { head :no_content }
      else
        format.html { render action: "edit" }
        format.json { render json: @receipt.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /receipts/1
  # DELETE /receipts/1.json
  def destroy
    if cannot? :delete, Receipt
      redirect_to @student
    end

    @receipt = Receipt.find(params[:id])
    # @receipt.destroy
    @receipt.is_void = true
    @receipt.save

    respond_to do |format|
      format.html { redirect_to student_path(@student, anchor:"receipts") }
      format.json { head :no_content }
    end
  end

  def reset_splits
    @receipt = Receipt.find params[:receipt_id]
    @receipt.receipt_items.each do |item|
      item.receipt_item_splits.each do |split|
        split.is_void = true
        split.save
      end
    end

    respond_to do |format|
      format.html { redirect_to student_path(@receipt.student) }
      format.json { head :no_content }
    end
  end

  protected
    def find_student_from_params_id
      @student = Student.find params[:student_id]
    end
end
