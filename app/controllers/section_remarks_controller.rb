class SectionRemarksController < InheritedResources::Base

  def auto
    @student = Student.find params[:student_id]

    unless @student.remark.blank?
      rank = 1
      @remark_sections = @student.remark.gsub("\r","").split("\n\n\n").map do |section|
        title = section.strip.lines.first
        unless section.strip.blank?
          section_remark = @student.section_remarks.create title: title, remark: section, rank: rank
          rank += 1

          if section_remark.title.include? "收據記錄"
            section_remark.rank = 999
            section_remark.save
          end
        end
      end

      @student.original_remark = @student.remark
      @student.remark = ""
      @student.save
    end
    redirect_to student_url(@student, anchor: 'remarks')
  end

  def update
    student_id = params[:student_id] || params[:section_remark][:student_id]
    @student = Student.find student_id
    @section_remark = SectionRemark.find params[:id]

    if params["commit"] == "附加備註"
      @section_remark.update_attributes params.require(:section_remark).permit(:title) # cannot allow :remark, otherwise will override!! and logic fails.

      unless params[:section_remark][:remark].blank?
        if @section_remark.remark.blank?
          @section_remark.remark = params[:section_remark][:remark]
        else
          remark_new_content = params[:section_remark][:remark]
          remark_new_content = remark_new_content.strip
          @section_remark.remark += "\n" + remark_new_content
        end
      end
      @section_remark.save
    end

    if params["commit"] == "更新分組備註"
      @section_remark.update_attributes params.require(:section_remark).permit(:title, :rank, :remark)
    end


    redirect_to student_url(@student, anchor:'remarks')

  end

  def new
    @section_remark = SectionRemark.new
    @section_remark.student = Student.find params[:student_id]
    @section_remark.remark = ""
    respond_to do |format|
      format.html # new.html.erb
      format.json { render json: @section_remark }
    end
  end

  def create
    @section_remark = SectionRemark.new(section_remark_params)
    @section_remark.student = Student.find params[:section_remark][:student_id]

    respond_to do |format|
      if @section_remark.save
        format.html { redirect_to student_url(@section_remark.student, anchor:'remarks'), notice: 'Section remark was successfully created.' }
        format.json { render :show, status: :created, location: @section_remark }
      else
        format.html { render :new }
        format.json { render json: @section_remark.errors, status: :unprocessable_entity }
      end
    end
  end

  def destroy
    @section_remark = SectionRemark.find(params[:id])
    student = @section_remark.student
    if @section_remark.title.blank? and @section_remark.remark.blank?
      @section_remark.destroy
    end

    redirect_to student_url(student, anchor:'remarks')
  end

  private

    def section_remark_params
      params.require(:section_remark).permit(:title, :remark, :student_id, :rank)
    end
end

