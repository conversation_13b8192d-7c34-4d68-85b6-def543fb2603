class VagasController < ApplicationController

  def index
    # require 'net/http'

    # begin
    #   c = Net::HTTP.new("dev.makzan.net")
    #   c.open_timeout = 1
    #   response = c.request_get("/vagas.txt")
    #   begin
    #     @data = ActiveSupport::JSON.decode(response.body)
    #   rescue MultiJson::DecodeError
    #     @data = []
    #     @error = "Cannot parse JSON"
    #   rescue JSON::ParserError
    #     @data = []
    #     @error = "Cannot parse JSON"
    #   end
    # rescue SocketError => e
    #   @data = []
    #   @error = e.message
    # rescue Net::OpenTimeout => e
    #   @data = []
    #   @error = e.message
    # end
  end

  def table
    # Read HTML content from the given URL
    require 'open-uri'
    # require 'nokogiri'
    url = "http://www.dsat.gov.mo/examvacancy/showrs.do"

    # Fetch the HTML content
    @html_content = open(url).read

    # Use "plain" layout
    render layout: "plain"
  end

end
