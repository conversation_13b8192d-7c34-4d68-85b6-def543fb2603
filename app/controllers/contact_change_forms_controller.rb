# encoding: utf-8
class ContactChangeFormsController < StudentApplicationController
  # GET /contact_change_forms
  # GET /contact_change_forms.json
  def index
    @contact_change_forms = @student.contact_change_forms

    respond_to do |format|
      format.html # index.html.erb
      format.json { render json: @contact_change_forms }
    end
  end

  # GET /contact_change_forms/1
  # GET /contact_change_forms/1.json
  def show
    @contact_change_form = @student.contact_change_forms.find(params[:id])

    respond_to do |format|
      format.html # show.html.erb
      format.json { render json: @contact_change_form }
    end
  end

  def preview
    @contact_change_form = @student.contact_change_forms.find(params[:id])

    respond_to do |format|
      format.html { render :layout => 'plain' }
      format.json { render json: @contact_change_form }
    end
  end

  def print
    @contact_change_form = @student.contact_change_forms.find(params[:id])
    respond_to do |format|
      format.html { render :layout => false}
      format.json { render json: @contact_change_form }
    end
  end

  # GET /contact_change_forms/new
  # GET /contact_change_forms/new.json
  def new
    @contact_change_form = @student.contact_change_forms.new

    # @contact_change_form.student = @student
    @contact_change_form.first_name = @student.first_name
    @contact_change_form.last_name = @student.last_name
    @contact_change_form.id_card_kind = @student.id_card_kind
    @contact_change_form.id_card = @student.id_card

    @contact_change_form.tel_no = @student.tel

    address_area = 1

    address_area = 2 if @student.chinese_address.include? "氹仔"
    address_area = 3 if @student.chinese_address.include? "氹仔"

    @contact_change_form.address_area = address_area
    @contact_change_form.address_street = @student.chinese_address

    @contact_change_form.address_street = @student.address if @student.chinese_address.blank?



    @contact_change_form.address_door_no = ""
    @contact_change_form.address_building_name = ""
    @contact_change_form.address_block_no = ""
    @contact_change_form.address_floor_no = ""

    # TODO: handle two types of licenses
    if @student.has_normal_license
      license = @student.normal_license

      @contact_change_form.license_no = license.no
    elsif @student.has_special_license
      license = @student.special_license

      @contact_change_form.license_no = license.no
    end

    respond_to do |format|
      format.html # new.html.erb
      format.json { render json: @contact_change_form }
    end
  end

  # GET /contact_change_forms/1/edit
  def edit
    @contact_change_form = @student.contact_change_forms.find(params[:id])
  end

  # POST /contact_change_forms
  # POST /contact_change_forms.json
  def create
    @contact_change_form = @student.contact_change_forms.new(params[:contact_change_form])

    @contact_change_form.student = @student

    respond_to do |format|
      if @contact_change_form.save
        format.html { redirect_to student_url(@student, anchor:'forms'), notice: 'Contact Change form was successfully created.' }
        format.json { render json: @contact_change_form, status: :created, location: @contact_change_form }
      else
        format.html { render action: "new" }
        format.json { render json: @contact_change_form.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /contact_change_forms/1
  # PUT /contact_change_forms/1.json
  def update
    @contact_change_form = ContactChangeForm.find(params[:id])

    respond_to do |format|
      if @contact_change_form.update_attributes(params[:contact_change_form])
        format.html { redirect_to student_url(@student, anchor:'forms'), notice: 'Health form was successfully updated.' }
        format.json { head :no_content }
      else
        format.html { render action: "edit" }
        format.json { render json: @contact_change_form.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /contact_change_forms/1
  # DELETE /contact_change_forms/1.json
  def destroy
    @contact_change_form = ContactChangeForm.find(params[:id])
    # @contact_change_form.destroy
    @contact_change_form.is_deleted = true
    @contact_change_form.deleted_at = DateTime.current
    @contact_change_form.save!

    respond_to do |format|
      format.html { redirect_to student_url(@student, anchor:'forms') }
      format.json { head :no_content }
    end
  end
end
