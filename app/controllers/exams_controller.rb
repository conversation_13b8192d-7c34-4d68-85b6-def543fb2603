#encoding: utf-8
class ExamsController < ApplicationController

  before_filter :find_student_from_params_id, except: [:mass_update_latest_exam_at, :new_attachment, :upload_attachment, :create_attachment, :cache_recent_exams]

  def index

    @exams = @student.exams

    respond_to do |format|
      format.html # index.html.haml
      format.json { render json: @exams }
    end
  end


  def show
    @exam = Exam.find(params[:id])


    respond_to do |format|
      format.html # show.html.haml
      format.json { render json: @exam }
    end
  end

  def attendence_form
    @exam = Exam.find params[:exam_id]
    @student = @exam.student
    @form_name = @exam.exam_type == "theory" ? "駕駛理論（交通）" : "駕駛實習"
    case @exam.exam_type
    when "theory"
      @form_name = "駕駛理論（交通）"
    when "road"
      @form_name = "駕駛實習"
    when "machine"
      @form_name = "駕駛技術（機器）"
    end


    @failed_exams = @student.failed_exams_before_exam(@exam)
    @failed_external = (@student.exams.count == 1 && (@exam.application_form.is_failed || @exam.application_form.is_absent))

    # Force to "New Exam" if @exam.application_form is marked as "new"
    @failed_exams = [] if @exam.application_form.is_failed == false


    if @failed_exams.size > 0 || @failed_external
      # Check exams from same person
      @last_failed_exam_at = nil
      @failed_exams.each do |exam|
        @last_failed_exam_at = exam.latest_exam_at.to_date if exam.latest_exam_at.to_date == @exam.application_form.failed_or_absent_at && exam.exam_type==@exam.exam_type
      end
    end


    render layout: false
  end



  def cache_recent_exams
    Exam.cache_recent_exams!
    @count = RecentExam.count
    render layout:false
  end


  def new_attachment
  end

  def upload_attachment
    uploaded_io = params[:attachment]

    filename = uploaded_io.original_filename
    filename_without_extension = filename.split('.')[0]


    array = filename_without_extension.split('-')
    if array.count == 3
      # Assuming filename format to be xxxxx-YYYY-0001.jpg where xxxxx is the exame code.
      @exam_code = array[0] + '/' + array[1]
    elsif array.count == 2
      # 2018-11-30: New format is xxxxxxxxx-0001.jpg, which is new number column besides exam number.
      @exam_code = array[0]
    elsif array.count == 1
      # 2019-09-05: in case there is no -0001 in the file name.
      @exam_code = array[0]
    end
    @exam = Exam.find_by_exam_type_and_exam_code :road, @exam_code

    # Save the file to temp and to paperclip
    # to temp
    temp_path = Rails.root.join('tmp', uploaded_io.original_filename)
    File.open(temp_path, 'wb') do |file|
      file.write(uploaded_io.read)
    end

    if @exam==nil
      @status = 'failed'
    else
      # move any existing file to files of the student before we override it.
      if @exam.attachment.exists?
        file = @exam.student.uploaded_files.new
        file.attachment = @exam.attachment
        file.save!
      end

      # finally, we save the newly uploaded file to paperclip
      file = File.open(temp_path)
      @exam.attachment = file

      if @exam.save!
        @status = 'success'
      else
        @status = 'failed'
      end
    end
  end

  def create_attachment
#     @exam = Exam.find_by_exam_type_and_exam_code :road, params[:exam][:exam_code] # using form_for so need [:exam][:exam_code]

#     if @exam.update_attributes(params[:exam])
#       @status = 'success'
#     else
#       @status = 'failed'
#     end
  end


  def new
    @exam = Exam.new


    respond_to do |format|
      format.html # new.html.haml
      format.json { render json: @exam }
    end
  end


  def edit
    @exam = Exam.find(params[:id])
    if @exam.exam_type == 'theory' && @exam.coach.nil?
      case @student.school.school_no
      when 19
        @exam.coach = Coach.find_by_coach_no 996
      when 26
        @exam.coach = Coach.find_by_coach_no 619
      when 37
        @exam.coach = Coach.find_by_coach_no 996
      when 13
        @exam.coach = Coach.find_by_coach_no 996
      when 30
        @exam.coach = Coach.find_by_coach_no 996
      when 41
        @exam.coach = Coach.find_by_coach_no 980
      when 7
        @exam.coach = Coach.find_by_coach_no 980
      end
    elsif @exam.exam_type == 'machine' && @exam.coach.nil?
      @exam.coach = Coach.find_by_coach_no 619
    end


  end


  def create
    @exam = Exam.new(params[:exam])
    exam_time = params[:exam_time]
    hour = 0
    min = 0
    if exam_time.length == 4
      hour = exam_time[0..1].to_i
      min = exam_time[2..3].to_i
    else
      hour = exam_time[0..1].to_i
      min = exam_time[3..4].to_i
    end
    @exam.exam_at = @exam.exam_at.to_date + hour.hours + min.minutes

    @exam.student = @student

    respond_to do |format|
      if @exam.save
        format.html { redirect_to student_exam_url(@student, @exam), notice: 'Exam was successfully created.' }
        format.json { render json: @exam, status: :created, location: @exam }
      else
        format.html { render action: "new" }
        format.json { render json: @exam.errors, status: :unprocessable_entity }
      end
    end
  end


  def update
    @exam = Exam.find(params[:id])
    exam_date = params[:exam_date]
    exam_time = params[:exam_time]
    hour = 8
    min = 0
    unless exam_time.blank?
      if exam_time.length == 4
        hour = exam_time[0..1].to_i
        min = exam_time[2..3].to_i
      else
        hour = exam_time[0..1].to_i
        min = exam_time[3..4].to_i
      end
    end

    old_status = @exam.status


    respond_to do |format|
      old_exam_route = @exam.exam_route
      old_exam_code = @exam.exam_code
      if @exam.update_attributes(params[:exam])

        exam_route_changed = (old_exam_route != @exam.exam_route)
        exam_code_changed = (old_exam_code != @exam.exam_code)

        # need to check if exam_at changed.
        exam_at_changed = false

        # @exam.exam_at = DateTime.new(@exam.exam_at.year, @exam.exam_at.month, @exam.exam_at.day, hour, min, 0)
        # update only if the two fields (date and time) are set and not empty.
        # staff may empty the date & time to delete it.
        unless exam_date.nil? && exam_time.nil?
          if exam_date.empty? && exam_time.empty?
            if @exam.has_extend_form
              form = @exam.latest_extend_form
              form.new_exam_at = nil
              form.save
            else
              @exam.exam_at = nil
              @exam.save!
            end
          else # not empty, has date and time
            # makzan: 2013-10-05, remove the feature to alter the extend form from the exam controller.
            #         otherwise any changes will accidentially alter the extend_form date.
            # if @exam.has_extend_form
            #   form = @exam.latest_extend_form
            #   old_exam_at = form.new_exam_at
            #   form.new_exam_at = exam_date.to_date + hour.hours + min.minutes
            #   form.save!
            #   exam_at_changed = true if old_exam_at != form.new_exam_at
            # else
            old_exam_at = @exam.exam_at
            begin
              @exam.exam_at = exam_date.to_date + hour.hours + min.minutes
              @exam.save!
            rescue => e
              @exam.errors.add :exam_at, e
              render action: "edit"
              return
            end
            exam_at_changed = true if old_exam_at != @exam.exam_at
            # end
          end

          @exam.update_latest_exam_at
        end

        # check if exam_at, exam_number, exam_route changed.
        # then add SMS record.
        if exam_at_changed # || exam_route_changed || exam_code_changed
          exam_type_wording_zh = "駕駛理論測驗"
          exam_type_wording_zh = "駕駛實習測驗" if @exam.exam_type == "road"
          exam_type_wording_zh = "機器測驗" if @exam.exam_type == "machine"

          exam_type_wording_en = "Theory Test"
          exam_type_wording_en = "Road Test" if @exam.exam_type == "road"
          exam_type_wording_en = "Mechanic Test" if @exam.exam_type == "machine"

          # send sms.
          if @exam.exam_type == "theory"
            create_sms_record(
              @exam.student,
              "#{@exam.student.school.name_zh} #{@exam.student.chinese_name} #{@exam.category.upcase} #{exam_type_wording_zh} 考試編號:#{@exam.exam_code} 日期:#{@exam.latest_exam_at.to_date} 時間:#{l @exam.latest_exam_at, format: :time},須於考試前,來校取回考試紙.僅供參考",
              "#{@exam.student.school.short_eng_name} #{@exam.student.eng_name} #{@exam.category.upcase} #{exam_type_wording_en} Test No:#{@exam.exam_code} Date:#{@exam.latest_exam_at.to_date} Time:#{l @exam.latest_exam_at, format: :time} Please come to our school to get your exam paper before your exam date. For Reference"
            )
          end
          if @exam.exam_type == "road"
            create_sms_record(
              @exam.student,
              "#{@exam.student.school.name_zh} #{@exam.student.chinese_name} #{@exam.category.upcase} #{exam_type_wording_zh} 考試編號:#{@exam.exam_code} 日期:#{@exam.latest_exam_at.to_date} 時間:#{l @exam.latest_exam_at, format: :time}.僅供參考",
              "#{@exam.student.school.short_eng_name} #{@exam.student.eng_name} #{@exam.category.upcase} #{exam_type_wording_en} Test No:#{@exam.exam_code} Date:#{@exam.latest_exam_at.to_date} Time:#{l @exam.latest_exam_at, format: :time}. For Reference"
            )
          end
        end

        # special case for theory exam.
        # create a new road exam after theory exam passes.
        if @exam.exam_type=='theory' and old_status != 'passed' and @exam.status == 'passed'
          exam = @exam.application_form.road_exam
          exam = Exam.new unless exam
          exam.student = @exam.student
          exam.remark = ''
          exam.status = 'waiting'
          exam.exam_type = 'road'
          exam.application_form = @exam.application_form
          exam.save
        end

        format.html { redirect_to student_url(@student, anchor:'exams'), notice: 'Exam was successfully updated.' }
        format.json { head :no_content }
      else
        format.html { render action: "edit" }
        format.json { render json: @exam.errors, status: :unprocessable_entity }
      end
    end
  end

  def destroy
    @exam = Exam.find(params[:id])
    @exam.destroy




    respond_to do |format|
      format.html { redirect_to student_exams_url(@student) }
      format.json { head :no_content }
    end
  end


#   def mass_update_latest_exam_at
#     Exam.all.each do |e|
#       e.update_latest_exam_at
#     end
#     raise "Done"
#   end


  protected
    def find_student_from_params_id
      @student = Student.find params[:student_id]
    end
end
