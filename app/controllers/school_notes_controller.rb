class SchoolNotesController < ApplicationController

  before_filter :find_school_from_params_id

  # GET /school_notes
  # GET /school_notes.json
  def index
    @school_notes = SchoolNote.all

    @school_note = SchoolNote.new

    respond_to do |format|
      format.html # index.html.erb
      format.json { render json: @school_notes }
    end
  end

  # GET /school_notes/1
  # GET /school_notes/1.json
  def show
    @school_note = SchoolNote.find(params[:id])

    respond_to do |format|
      format.html # show.html.erb
      format.json { render json: @school_note }
    end
  end

  # GET /school_notes/new
  # GET /school_notes/new.json
  def new
    @school_note = SchoolNote.new

    respond_to do |format|
      format.html # new.html.erb
      format.json { render json: @school_note }
    end
  end

  # GET /school_notes/1/edit
  def edit
    @school_note = SchoolNote.find(params[:id])
  end

  # POST /school_notes
  # POST /school_notes.json
  def create
    @school_note = SchoolNote.new(params[:school_note])

    @school_note.school = @school
    @school_note.staff = current_staff

    respond_to do |format|
      if @school_note.save
        format.html { redirect_to school_school_notes_path, notice: 'School note was successfully created.' }
        format.json { render json: @school_note, status: :created, location: @school_note }
      else
        format.html { render action: "new" }
        format.json { render json: @school_note.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /school_notes/1
  # PUT /school_notes/1.json
  def update
    @school_note = SchoolNote.find(params[:id])

    respond_to do |format|
      if @school_note.update_attributes(params[:school_note])
        format.html { redirect_to [@school,@school_note], notice: 'School note was successfully updated.' }
        format.json { head :no_content }
      else
        format.html { render action: "edit" }
        format.json { render json: @school_note.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /school_notes/1
  # DELETE /school_notes/1.json
  def destroy
    @school_note = SchoolNote.find(params[:id])

    if @school_note.striked
      @school_note.deleted = true
    else
      @school_note.striked = true
    end

    @school_note.save

    respond_to do |format|
      format.html { redirect_to school_school_notes_path }
      format.json { head :no_content }
    end
  end

  protected
    def find_school_from_params_id
      @school = School.friendly.find params[:school_id]
    end
end
