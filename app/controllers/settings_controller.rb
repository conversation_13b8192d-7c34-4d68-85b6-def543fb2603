class SettingsController < ApplicationController
  def reset
    if params[:secret] == "thisismakzan!"
      Setting.delete_all

      s1 = Setting.new
      s1.key = "sms_deliver_enabled"
      s1.value = "true"
      s1.save

      s2 = Setting.new
      s2.key = "sms_hardcode_enabled"
      s2.value = "true"
      s2.save

      s3 = Setting.new
      s3.key = "sms_hardcode_to"
      s3.value = "85366335344"
      s3.save

      render json: Setting.all
    else
      render json: {result: "declined"}
    end
  end

  def get
    if params[:secret] == "thisismakzan!"
      if params[:key].nil?
        render json: {result: "Args not correct"}
        return
      end
      s = Setting.find_by_key params[:key]
      render json: s
    else
      render json: {result: "declined"}
    end
  end

  def set
    if params[:secret] == "thisismakzan!"
      if params[:key].nil? || params[:value].nil?
        render json: {result: "Args not correct"}
        return
      end
      s = Setting.find_by_key params[:key]
      s.value = params[:value]
      s.save!
      render json: s
    else
      render json: {result: "declined"}
    end
  end
end
