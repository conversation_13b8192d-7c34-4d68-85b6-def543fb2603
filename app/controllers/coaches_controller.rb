class CoachesController < ApplicationController
  # GET /coaches
  # GET /coaches.json
  def index
    @coaches = Coach.order(:school_id, :coach_no).non_deleted

    # find empty short_form
    @coaches.each do |c|
      if c.short_form.blank?
        c.set_initial_short_form
        c.save
      end
    end

    respond_to do |format|
      format.html # index.html.haml
      format.json { render json: @coaches }
    end
  end

  def summary
    @coach = Coach.find(params[:coach_id])

    @year = params[:year] || Date.current.year
    @month = params[:month] || Date.current.month
    date = "#{@year}-#{@month}-01".to_datetime

    # @theory_exams = Exam.where(coach_id: @coach.id, exam_type:'theory').where('latest_exam_at >= ? and latest_exam_at <= ?', date.at_beginning_of_month, date.at_end_of_month).order(:latest_exam_at)
    @road_exams = Exam.where(coach_id: @coach.id, exam_type:'road').where('latest_exam_at >= ? and latest_exam_at <= ?', date.at_beginning_of_month, date.at_end_of_month).order(:latest_exam_at)

  end

  def trash
    @coaches = Coach.order(:school_id).where(is_deleted:true)

    respond_to do |format|
      format.html # index.html.haml
      format.json { render json: @coaches }
    end
  end

  def recover
    @coach = Coach.find(params[:coach_id])
    @coach.is_deleted = false
    @coach.save
    redirect_to coaches_url
  end

  # GET /coaches/1
  # GET /coaches/1.json
  def show
    @coach = Coach.find(params[:id])

    @year = params[:year] || Date.current.year
    @month = params[:month] || Date.current.month
    date = "#{@year}-#{@month}-01".to_datetime

    @theory_exams = Exam.where(coach_id: @coach.id, exam_type:'theory').where('latest_exam_at >= ? and latest_exam_at <= ?', date.at_beginning_of_month, date.at_end_of_month).order(:latest_exam_at)
    @road_exams = Exam.where(coach_id: @coach.id, exam_type:'road').where('latest_exam_at >= ? and latest_exam_at <= ?', date.at_beginning_of_month, date.at_end_of_month).order(:latest_exam_at)

    respond_to do |format|
      format.html # show.html.haml
      format.json { render json: @coach }
    end
  end

  # GET /coaches/new
  # GET /coaches/new.json
  def new
    @coach = Coach.new

    respond_to do |format|
      format.html # new.html.haml
      format.json { render json: @coach }
    end
  end

  # GET /coaches/1/edit
  def edit
    @coach = Coach.find(params[:id])
  end

  # POST /coaches
  # POST /coaches.json
  def create
    @coach = Coach.new(params[:coach])

    respond_to do |format|
      if @coach.save
        format.html { redirect_to @coach, notice: 'Coach was successfully created.' }
        format.json { render json: @coach, status: :created, location: @coach }
      else
        format.html { render action: "new" }
        format.json { render json: @coach.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /coaches/1
  # PUT /coaches/1.json
  def update
    @coach = Coach.find(params[:id])

    respond_to do |format|
      if @coach.update_attributes(params[:coach])
        format.html { redirect_to @coach, notice: 'Coach was successfully updated.' }
        format.json { head :no_content }
      else
        format.html { render action: "edit" }
        format.json { render json: @coach.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /coaches/1
  # DELETE /coaches/1.json
  def destroy
    @coach = Coach.find(params[:id])
    @coach.is_deleted = true
    @coach.deleted_at = DateTime.current
    @coach.save!

    respond_to do |format|
      format.html { redirect_to coaches_url }
      format.json { head :no_content }
    end
  end
end
