#encoding: utf-8
class ApplicationController < ActionController::Base
  protect_from_forgery

  before_filter :student_search, :define_server_env, :define_return_to

  before_filter :verify_staff_session, except: [:check_unique, :update_expense, :deliver, :cache_recent_exams, :cache_waiting_result_students]

  before_filter :receipt_split_control, except: [:check_unique, :update_expense, :deliver, :cache_recent_exams, :cache_waiting_result_students]

  before_filter :run_once

  before_action :set_paper_trail_whodunnit

  protected

  def run_once
    # 2017.12.09 Fix missing SMS from for SHL school.
    # Message.where(from: nil).each do |m|
    #   m.from = "28570826"
    #   m.save
    # end
  end

  def define_server_env
    @env = 'production'
    if request.host[-4,4] == '.dev' || request.port == 8080 || request.port == 3000
      @env = 'test'
    end
  end

  def define_return_to
    session[:return_to] = request.referer
  end

  def student_search
    @q = Student.ransack params[:q]
  end

  def receipt_split_control
    @show_split_panel = false

    if session[:is_splitting] && session[:split_item_id] && session[:split_student_id]
      @split_student = Student.find session[:split_student_id]
      @split_receipt_item = ReceiptItem.find session[:split_item_id]


      @receipt_item_split = @split_receipt_item.receipt_item_splits.new
      # auto fill the expense / turnover
      @receipt_item_split.amount = @split_receipt_item.splits_amount_remain

      if @receipt_item_split.amount.nil?
        @show_split_panel = false
        return
      end

      @receipt_item_split.expense = session[:split_expense] || @split_receipt_item.suggest_expense

      price = @split_receipt_item.price
      price = 0 if @split_receipt_item.price.nil?

      @receipt_item_split.turnover = price - @receipt_item_split.expense
      # auto fill the kind
      @receipt_item_split.kind = '亞洲保險' unless @receipt_item_split.receipt_item.description.index('保險').nil?
      # @receipt_item_split.kind = '本校' unless @receipt_item_split.receipt_item.description.index('理論(交通)課程').nil? # Disabled at 2023-11-17, for 揀師傅 not 學校
      @receipt_item_split.kind = '交通事務局' unless @receipt_item_split.receipt_item.description.index('覆稟').nil?
      @receipt_item_split.kind = '交通事務局' unless @receipt_item_split.receipt_item.description.index('入稟').nil?
      @receipt_item_split.kind = '交通事務局' unless @receipt_item_split.receipt_item.description.index('延期費用').nil?
      @receipt_item_split.kind = '交通事務局' unless @receipt_item_split.receipt_item.description.index('提前費用').nil?
      # auto fill the
      @receipt_item_split.coach_id = session[:split_coach_id] unless session[:split_coach_id].nil?

      @show_split_panel = session[:is_splitting]
    end
  end

  def verify_staff_session
    if controller_name != 'logins' && current_staff == nil
      redirect_to login_path
    end
  end

  # Finds the User with the ID stored in the session with the key
  # :current_staff_id This is a common way to handle staff login in
  # a Rails application; logging in sets the session value and
  # logging out removes it.
  def current_staff
    @_current_staff ||= session[:current_staff_id] &&
      Staff.find_by_id(session[:current_staff_id])
  end
  helper_method :current_staff

  # for cancan
  def current_user
    current_staff
  end


  def generate_hash(length=8)
    str = [('a'..'z'),('A'..'Z'),(0..9)].map{|i| i.to_a}.flatten
    str.shuffle.join[0...length]
  end
  def generate_alphabet_hash(length=4)
    %w(a b c d e f g h i j k m n p q r s t u v w x y z).shuffle.join[0...length]
  end
  def generate_number_hash(length=4)
    (2..9).to_a.shuffle.join[0...length]
  end


  def create_sms_record(student, content_zh, content_en)
    sms = Message.new
    sms.deliver_at = DateTime.now
    sms.sent = 0
    sms.approved = 1
    sms.from = student.school.tel
    sms.to = student.tel.split(' ')[0]
    if student.language == "中文"
      if student.chinese_name.blank?
        sms.content = content_en
      else
        sms.content = content_zh
      end
    else
      sms.content = content_en
    end

    sms.student = student
    sms.save
  end



end
