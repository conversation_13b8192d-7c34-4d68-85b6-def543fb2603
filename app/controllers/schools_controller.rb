class SchoolsController < ApplicationController
  # GET /schools
  # GET /schools.json
  def index
    @schools = School.all

    respond_to do |format|
      format.html # index.html.haml
      format.json { render json: @schools }
    end
  end

  # GET /schools/1
  # GET /schools/1.json
  def show
    @school = School.friendly.find(params[:id])

    respond_to do |format|
      format.html # show.html.haml
      format.json { render json: @school }
    end
  end

  def show_receipts
    @start_time = Time.now
    @school = School.friendly.find params[:id]

    @year = params[:year] || Date.current.year
    @month = params[:month] || Date.current.month

    beginning_of_month = "#{@year}-#{@month}-01".to_datetime.beginning_of_month
    end_of_month = "#{@year}-#{@month}-01".to_datetime.end_of_month

    @receipts = @school.receipts.where('created_at >= ? and created_at <= ?', beginning_of_month, end_of_month).order('id DESC').paginate(page: params[:page], per_page: 100)

    # @receipts = @school.receipts.order('id DESC').paginate(page: params[:page], per_page: 100)
  end

  def receipts_report
    @school = School.friendly.find params[:id]

    @year = params[:year] || Date.current.year
    @month = params[:month] || Date.current.month

    beginning_of_month = "#{@year}-#{@month}-01".to_datetime.beginning_of_month
    end_of_month = "#{@year}-#{@month}-01".to_datetime.end_of_month

    # Testing the sum and stat 2025-02-26
    @all_receipts = @school.receipts.where('created_at >= ? and created_at <= ?', beginning_of_month, end_of_month)
    @total = 0
    @cash_total = 0
    @credit_card_total = 0
    @mpay_total = 0
    @wechat_total = 0
    @all_receipts.each do |r|
      @total += r.total_price unless r.is_void
      @cash_total += r.cash_amount unless r.is_void
      @credit_card_total += r.credit_card_amount unless r.is_void
      @mpay_total += r.mpay_amount unless r.is_void
      @wechat_total += r.wechat_amount unless r.is_void
    end

    # Testing the sum with receipt split item
    # @all_receipts = @school.receipts.where('created_at >= ? and created_at <= ?', beginning_of_month, end_of_month)
    # @all_receipts.each do |r|
    #   r.receipt_items.each do |item|
    #     item.receipt_item_splits.each do |split|
    #       raise split.inspect
    #     end
    #   end
    # end
  end

  def show_void_receipts
    @start_time = Time.now
    @school = School.friendly.find params[:id]

    @year = params[:year] || Date.current.year
    @month = params[:month] || Date.current.month

    if @month == "0"
      beginning_of_month = "#{@year}-01-01".to_datetime.beginning_of_month
      end_of_month = "#{@year}-12-31".to_datetime.end_of_month
    else
      beginning_of_month = "#{@year}-#{@month}-01".to_datetime.beginning_of_month
      end_of_month = "#{@year}-#{@month}-01".to_datetime.end_of_month
    end

    @receipts = @school.receipts.where(is_void:true).where('created_at >= ? and created_at <= ?', beginning_of_month, end_of_month).order('id DESC').paginate(page: params[:page], per_page: 100)
  end

  def show_non_handled_receipts

    @start_time = Time.now
    @school = School.friendly.find params[:id]
    # results = []
    # .each do |r|
      # results << Receipt.find(r.receipt_id)
    # end
    # @non_split_receipts = NonSplitReceipt.where(is_processed:false, school_id: @school.id).order("receipt_id DESC").paginate(page:params[:page], per_page:100)

    if params[:all]
      @non_split_items = ReceiptItem.where(school_id:@school.id, has_split:false).order("id ASC").paginate(page:params[:page], per_page: 100)
    else
      @year = params[:year] || Date.current.year
      @month = params[:month] || Date.current.month

      beginning_of_month = "#{@year}-#{@month}-01".to_datetime.beginning_of_month
      end_of_month = "#{@year}-#{@month}-01".to_datetime.end_of_month

      @non_split_items = ReceiptItem.where('created_at >= ? and created_at <= ?', beginning_of_month, end_of_month).where(school_id:@school.id, has_split:false).order("id DESC").paginate(page:params[:page], per_page: 100)
    end

    # @receipts = results.paginate(page:params[:page], per_page:100)
    # @receipts = @school.non_handled_receipts.paginate(page: params[:page], per_page: 100)

  end

  def show_students
    start_time = Time.now
    @school = School.friendly.find(params[:id])
    # @students = @school.students.order('id DESC').page params[:page]
    @students = @school.students.order('code IS NOT NULL, code DESC, id').paginate(page: params[:page])
    # @students = @school.students.order('id DESC').paginate(page: params[:page])

    end_time = Time.now
    @benchmark = end_time - start_time

    if params[:layout] == "grid"
      if params[:gender] == "female"
        @students = @school.students.where(gender:'female').order('id DESC').paginate(page: params[:page], per_page:100)
      else
        @students = @school.students.order('code DESC').paginate(page: params[:page], per_page:100)
      end
      render "show_students_grid"
    end

  end

  def show_students_today_new
    start_time = Time.now
    @school = School.friendly.find(params[:id])
    # @students = @school.students.order('id DESC').page params[:page]
    @students = @school.students.where('created_at >= ?', Date.current.beginning_of_day).order('id DESC').paginate(page: params[:page])
    # @students = @school.students.order('id DESC').paginate(page: params[:page])

    end_time = Time.now
    @benchmark = end_time - start_time

  end

  def show_students_waiting_result
    start_time = Time.now
    @school = School.friendly.find(params[:id])

    @waiting_students = WaitingResultStudent.where(school_id:@school.id)

    @exams = []
    @waiting_students.each do |exam|
      @exams.push Exam.find exam.exam_id
    end

    # @students = @school.students_with_waiting_result_exam
    # @exams = []
    # @students.each do |student|
    #   student.waiting_exams.each do |e|
    #     next if e.exam_at_include_extend_form.nil?
    #     next if e.has_extend_form && ! e.has_extended
    #     @exams << e if (e.exam_at_include_extend_form.past? || e.exam_at_include_extend_form.to_date == Date.current)
    #   end
    # end

    @exams = @exams.sort { |x, y|
      y.exam_at_include_extend_form <=> x.exam_at_include_extend_form
    }

    # @exams.sort_by!
    # raise "Done"

    # raise @exams.to_yaml


    end_time = Time.now
    @benchmark = end_time - start_time
  end

  def show_students_exam_absent
    start_time = Time.now
    @school = School.friendly.find params[:id]
    exams = Exam.where(status: :absent)
    @exams = []
    exams.each do |e|
      next if e.student.nil?
      next if e.student.school != @school
      next if e.exam_at_include_extend_form.nil?
      next if e.days_since_exam > 100
      @exams << e
    end
    end_time = Time.now
    @benchmark = end_time - start_time
  end

  def show_students_exam_extended
    start_time = Time.now
    @school = School.friendly.find params[:id]
    extend_forms = ExtendForm.all
    @exams = []
    extend_forms.each do |ef|
      # next if e.exam_at_include_extend_form.nil?
      next if ef.exam.nil?
      next if ef.exam.student.nil?
      next if ef.exam.student.school != @school
      # next if ef.
      @exams << ef.exam if ef.new_exam_at.nil?
    end
    end_time = Time.now
    @benchmark = end_time - start_time
  end

  def show_students_exam(type="theory")
    start_time = Time.now
    session[:year] = (params[:year] || session[:year] || Date.current.year).to_i
    session[:month] = (params[:month] || session[:month] || Date.current.month).to_i
    @school = School.friendly.find params[:id]
    # raise @school.to_yaml
    exams = Exam.where(exam_type: type)
    @exams = []
    @date_selection_years = []
    @date_selection_months = []
    exams.each do |e|
      next if e.exam_at_include_extend_form.nil?
      next if e.student.nil?
      next if e.student.school != @school
      @date_selection_years << e.exam_at_include_extend_form.year
      @date_selection_months << e.exam_at_include_extend_form.month
      next if e.exam_at_include_extend_form.year != session[:year]
      next if e.exam_at_include_extend_form.month != session[:month]
      @exams << e
    end

    # Sort by exam_at DESC
    @exams = @exams.sort { |x,y| y.exam_at_include_extend_form <=> x.exam_at_include_extend_form}

    # Years/Months range
    @date_selection_years = @date_selection_years.uniq.sort
    @date_selection_months = @date_selection_months.uniq.sort

    # @exams.paginate(page: params[:page])
    end_time = Time.now
    @benchmark = end_time - start_time
  end

  def show_students_exam_theory
    show_students_exam("theory")
  end

  def show_students_exam_road
    show_students_exam("road")
  end

  def show_students_exam_machine
    show_students_exam("machine")
  end

  def show_students_theory_expiry
    @school = School.friendly.find(params[:id])
    @result = Exam.where('exam_type=? and status=? and latest_exam_at>=?', 'theory', 'passed', Date.current-750.days).order("latest_exam_at")
    @exams = []
    @result.each do |exam|
      @exams << exam unless exam.student.has_passed_any_road_exams?
    end
    @exams = @exams.paginate(page:params[:page])
  end

  def show_students_theory_passed_without_exam_date
    @school = School.friendly.find(params[:id])
    @result = Exam.where('exam_type=? and status=? and latest_exam_at>=?', 'theory', 'passed', Date.current-91.days).order("latest_exam_at").order(exam_at: :desc)
    @exams = []
    @result.each do |exam|
      # road_exam = exam.application_form.road_exam
      # unless road_exam.nil?
      #   if road_exam.status == :waiting and road_exam.exam_at.blank?
      #     @exams << exam
      #   end
      # end
      if exam.student.school == @school
        @exams << exam
      end
    end
    # @exams = @exams.paginate(page:params[:page])
  end

  def show_students_exam_soon
    start_time = Time.now

    @school = School.friendly.find params[:id]
    @recent_exams = RecentExam.where(school_id:@school.id).order(:exam_at)

    @exams = []
    @recent_exams.each do |exam|
      e = Exam.where(id: exam.exam_id).first
      unless e.nil?
        @exams.push Exam.find e
      end
    end
#
#     #today
#     exams = Exam.exam_at_certain_date Date.current
#     extend_forms = ExtendForm.new_exam_at_certain_date Date.current
#     extend_forms.each do |ef|
#       exams << ef.exam
#     end
#     # tomorrow
#     exams.concat Exam.exam_at_certain_date Date.tomorrow
#     extend_forms = ExtendForm.new_exam_at_certain_date Date.tomorrow
#     extend_forms.each do |ef|
#       exams << ef.exam
#     end
#
#     @exams = []
#     exams.each do |e|
#       next if e.exam_at.nil? #_include_extend_form.nil?
#       next if e.student.nil?
#       next if e.student.school != @school
#       @exams << e if (e.exam_at_include_extend_form.to_date == Date.current || e.exam_at_include_extend_form.to_date == Date.tomorrow)
#       # @exams << e if (e.exam_at.to_date == Date.current || e.exam_at.to_date == Date.tomorrow)
#     end
#
#     @exams.sort!{ |x,y| x.exam_at_include_extend_form <=> y.exam_at_include_extend_form}

    end_time = Time.now
    @benchmark = end_time - start_time
  end

  def show_students_failed
    start_time = Time.now
    @school = School.friendly.find(params[:id])

    # search from exam, not from student
    exams = Exam.where(status: 'failed', latest_exam_at: 100.days.ago..Date.current).order('latest_exam_at')

    @exams = []
    exams.each do |e|
      next if e.student.school != @school
      next if e.student.exams.size > 1 && e.student.has_passed_after_exam(e)
      if e.student.exams.size > 1 && e.student.has_waiting_after_exam(e)
        exam = e.student.waiting_exam_after_exam(e)
        next if exam.exam_at != nil  # show students with waiting but not yet has new exam date.
      end
      @exams << e
    end

    end_time = Time.now
    @benchmark = end_time - start_time
  end

  # GET /schools/new
  # GET /schools/new.json
  def new
    @school = School.new

    respond_to do |format|
      format.html # new.html.haml
      format.json { render json: @school }
    end
  end

  # GET /schools/1/edit
  def edit
    @school = School.friendly.find(params[:id])
  end

  # POST /schools
  # POST /schools.json
  def create
    @school = School.new(params[:school])
    @school.id = @school.school_no

    respond_to do |format|
      if @school.save
        format.html { redirect_to @school, notice: 'School was successfully created.' }
        format.json { render json: @school, status: :created, location: @school }
      else
        format.html { render action: "new" }
        format.json { render json: @school.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /schools/1
  # PUT /schools/1.json
  def update
    @school = School.friendly.find(params[:id])

    respond_to do |format|
      if @school.update_attributes(params[:school])
        format.html { redirect_to @school, notice: 'School was successfully updated.' }
        format.json { head :no_content }
      else
        format.html { render action: "edit" }
        format.json { render json: @school.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /schools/1
  # DELETE /schools/1.json
  def destroy
    @school = School.friendly.find(params[:id])
    @school.destroy

    respond_to do |format|
      format.html { redirect_to schools_url }
      format.json { head :no_content }
    end
  end


  def move_all_remarks
    raise "secret not correct" if params[:secret] != "thisismakzan"
    School.all.each do |school|
      school.remark = ''
      school.remarks.each do |r|
        school.remark += r.content
        school.remark += "\n"
      end
      if school.save
      else
        # raise school.to_yaml
      end
    end
    raise "done"
  end

end
