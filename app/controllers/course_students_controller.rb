#encoding: utf-8
class CourseStudentsController < ApplicationController
  def index
    # list all course students that are not bound to any student records.
    @course_students = CourseStudent.where(student_id: nil)
  end

  def all
    @course_students = CourseStudent.order("id DESC").paginate(page: params[:page])
  end

  def new
    if params[:student_id]
      @student = Student.find params[:student_id]
      @is_bound_to_student = true
      @course_student = CourseStudent.new

      if !@student.course_student.nil?
        redirect_to student_url(@student, anchor: 'exams')
      end
    else
      @is_bound_to_student = false
      @course_student = CourseStudent.new
    end
  end

  def edit_valid_till
    @course_student = CourseStudent.find params[:id]
  end

  def create
    # create from student page
    if params[:student_id]
      @student = Student.find params[:student_id]
      @course_student = CourseStudent.create params[:course_student]
      @course_student.student = @student
      @course_student.id_card_raw = @student.id_card_raw
      if @course_student.student.try(:theory_exams).try(:last).try(:exam_at_include_extend_form)
        @course_student.valid_till = @course_student.student.theory_exams.last.exam_at_include_extend_form + 12.hours
      else
        @course_student.valid_till = 2.days.since
      end
      @course_student.save


      # password generation
      hash = generate_alphabet_hash + generate_number_hash
      @course_student.password = hash
      @course_student.plain_password = hash
      @course_student.save

      # send sms
      create_sms_record(
        @student,
        "#{@student.school.name_zh} #{@student.chinese_name} 網上學習帳號 網址：https://course2.chungwaDS.org 用戶代號：#{@course_student.id_card_raw} 密碼：#{@course_student.plain_password} 僅供參考",
        "#{@student.school.short_eng_name} #{@student.eng_name} Web Course Account Website: https://course2.chungwaDS.org User Name: #{@course_student.id_card_raw} Password: #{@course_student.plain_password} For Reference.")

      redirect_to student_url(@student, anchor:'exams')

    else # create from raw input
      @course_student = CourseStudent.create params[:course_student]
      @course_student.plain_password = params[:course_student][:password]
      # @course_student.valid_till = 10.days.since
      @course_student.id_card_raw = @course_student.id_card_raw.gsub('(','').gsub(')','')
      @course_student.save

      # password generation
      # TODO: to be extracted because now cloned
      hash = generate_number_hash
      @course_student.password = hash
      @course_student.plain_password = hash
      @course_student.save

      # send sms
      # sms = Message.new
      # sms.deliver_at = DateTime.now
      # sms.sent = 0
      # sms.approved = 1
      # sms.to = params[:course_student][:tel]
      # sms.content = "#{@student.school.name_zh} #{@student.chinese_name} 網上學習帳號 網址：http://course.chungwaDS.org 用戶代號：#{@course_student.id_card_raw} 密碼：#{@course_student.plain_password} 僅供參考"

      # sms.student = @student
      # sms.save


      redirect_to course_students_url
    end
  end

  def edit
    @course_student = CourseStudent.find params[:id]
  end

  def update
    @course_student = CourseStudent.find params[:id]
    if @course_student.update_attributes(params[:course_student])
      if @course_student.valid_till.nil?
        @course_student.errors.add(:valid_till, "Date format error.")
        render action: "edit"
        return
      end

      @course_student.valid_till += 23.hours
      @course_student.has_post_remote = false
      @course_student.save
      if !@course_student.student.nil?
        redirect_to student_url(@course_student.student, anchor: 'exams')
      else
        redirect_to course_students_url
      end
    else
      render action: "edit"
    end
  end

  def print
    # @student = Student.find params[:student_id]
    @course_student = CourseStudent.find params[:course_student_id]

    render layout: 'print'
  end

  def destroy
    @course_student = CourseStudent.find params[:id]
    @student = @course_student.student
    @course_student.destroy

    if @student
      redirect_to student_url(@student, anchor: 'exams')
    else
      redirect_to course_students_url
    end
  end

  def reset_password
    @course_student = CourseStudent.find params[:course_student_id]
    hash = generate_number_hash
    @course_student.password = hash
    @course_student.plain_password = hash
    @course_student.has_post_remote = false
    @course_student.save

    redirect_to course_students_url
  end

  protected
  def generate_number_hash(length=4)
    (0..9).to_a.shuffle.join[0...length]
  end
end
