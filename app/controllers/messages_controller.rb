#encoding: utf-8
class MessagesController < ApplicationController
  def index
    @messages = Message.order('id DESC')
    
    unless params[:status].blank?
      @messages = @messages.where(is_success: false) if params[:status] == "failed"
      @messages = @messages.where(sent: false) if params[:status] == "pending"
    end
    
    @messages = @messages.paginate(page: params[:page])
  end

  def deliver
    # check if deliver enabled
    s = Setting.find_by_key "sms_deliver_enabled"
    if s.nil? or s.value != "true"
      render json: {status: "disabled"}
      return
    end

    @messages = Message.where(is_success: nil, approved: true).limit(5)
    sent_count = 0
    @messages.each do |m|


      if m.to == "." or m.to == ".." or m.to == "..."
        m.sent = true
        m.is_success = false
        m.save
        sent_count += 1
      else

        require 'net/http'
        require 'uri'

        # uri = URI.parse("https://chungwa-sms.makzan.now.sh/api/")

        uri = URI.parse("https://chungwa-sms-makzan.vercel.app/api/")
        request = Net::HTTP::Post.new(uri.path)

        to = m.to
        # to = '853' + to if to.length == 8

        # hardcode
        is_hardcoded_setting = Setting.find_by_key "sms_hardcode_enabled"
        hardcode_to_setting = Setting.find_by_key "sms_hardcode_to"
        if !is_hardcoded_setting.nil? && is_hardcoded_setting.value == "true"
          to = hardcode_to_setting.value
        end

        request.set_form_data(
          "authKey" => "chungwa-system-key",
          "mobileNumber" => to,
          "message" => m.content.sub(":","："),
        )

        req_options = {
          use_ssl: uri.scheme == "https",
        }

        response = Net::HTTP.start(uri.hostname, uri.port, req_options) do |http|
          http.request(request)
        end

        m.sent = true
        sent_count += 1

        if response.kind_of?(Net::HTTPSuccess)
          # store response.message_id
          m.is_success = true
          m.response = response.to_yaml.to_s
          m.save
        else
          m.is_success = false
          m.response = response.to_yaml.to_s
          m.save
          raise response.to_yaml
          # check response.error.message and/or response.http
          #logger.debug response.error.message
          # sms.remark = response.error.message
        end
      end
    end
    # raise "Cannot send SMS in Local" if request.host[-4,4] == '.dev'
    render json: {
      status: :success,
      messages_sent: sent_count
    }
  end

  def deliver_old
    # check if deliver enabled
    s = Setting.find_by_key "sms_deliver_enabled"
    if s.nil? or s.value != "true"
      render json: {status: "disabled"}
      return
    end

    @messages = Message.where(sent: false, pproved: true).limit(5)
    sent_count = 0
    @messages.each do |m|
      nexmo = Nexmo::Client.new('f0006c47', '437317a8') #<EMAIL>
      nexmo.http.verify_mode = OpenSSL::SSL::VERIFY_NONE
      to = m.to
      to = '853' + to if to.length == 8

      # hardcode
      is_hardcoded_setting = Setting.find_by_key "sms_hardcode_enabled"
      hardcode_to_setting = Setting.find_by_key "sms_hardcode_to"
      if !is_hardcoded_setting.nil? && is_hardcoded_setting.value == "true"
        to = hardcode_to_setting.value
      end

      response = nexmo.send_message({
          from: '853' + m.from,
          to: to,
          text: m.content,
          type: 'unicode'
        })
      m.sent = true
      sent_count += 1

      if response.ok?
        # store response.message_id
        m.is_success = true
      else
        m.is_success = false
        # raise response.to_yaml
        # check response.error.message and/or response.http
        #logger.debug response.error.message
        # sms.remark = response.error.message
      end

      m.save
    end
    # raise "Cannot send SMS in Local" if request.host[-4,4] == '.dev'
    render json: {
      status: :success,
      messages_sent: sent_count
    }
  end

  def new
    @message = Message.new
    unless params[:to].nil?
      to = params[:to].split ' '
      @message.to = to[0]
      @message.student = Student.find_by_tel params[:to]
      @message.from = @message.student.school.tel
      if @message.student.language == "中文"
        @message.content = @message.student.school.name_zh + "駕駛學校："
      else
        @message.content = @message.student.school.short_eng_name + ":"
      end
    end
  end

  def create
    @message = Message.new(params[:message])
    @message.from = @message.student.school.tel
    @message.deliver_at = DateTime.current
    @message.sent = false
    @message.approved = true

    respond_to do |format|
      if @message.save
        format.html { redirect_to student_url(@message.student, anchor:'sms'), notice: 'sms was successfully created.' }
        format.json { render json: @message, status: :created, location: @message }
      else
        format.html { render action: "new" }
        format.json { render json: @message.errors, status: :unprocessable_entity }
      end
    end
  end
  
  def resend
    @message = Message.find params[:message_id]
    if @message.sent==true and @message.is_success == false
      @message.sent = false
      @message.is_success = nil
      @message.save
    end
      
    redirect_to [:messages, {status: "failed"}]
  end
end
