# encoding: utf-8
class ReceiptItemSplitsController < ApplicationController

  def index
    @splits = ReceiptItemSplit.order('expense desc')
  end

  def cancel
    session[:is_splitting] = false
    session[:split_expense] = nil
    # raise session[:return_to].to_yaml
    redirect_to session[:return_to]
    # redirect_to root_path
  end

  def reset
    receipt_item = ReceiptItem.find params[:receipt_item_id]
    receipt_item_split = ReceiptItemSplit.find params[:receipt_item_split_id]
    receipt_item_split.is_void = true
    receipt_item_split.save
    redirect_to receipt_item.receipt.student
  end

  def skip
    session[:is_splitting] = true
    session[:split_expense] = nil
    receipt = ReceiptItem.find(params[:receipt_item_id]).receipt

    @receipt_item = receipt.next_non_split_item_with_skip params[:receipt_item_id].to_i

    if !@receipt_item.nil?
      session[:split_student_id] = @receipt_item.receipt.student_id
      session[:split_item_id] = @receipt_item.id
    end

    redirect_to session[:return_to]
  end

  def set
    session[:is_splitting] = true
    session[:split_expense] = nil
    @receipt_item = ReceiptItem.find params[:receipt_item_id]

    if !@receipt_item.nil?
      session[:split_student_id] = @receipt_item.receipt.student_id
      session[:split_item_id] = @receipt_item.id
    end

    redirect_to session[:return_to]
  end

  def update_expense
    session[:split_expense] = params[:expense]
    render json: 'success'
  end

  def new
    session[:is_splitting] = true
    @receipt_item = ReceiptItem.find params[:receipt_item_id]
    session[:split_student_id] = @receipt_item.receipt.student_id
    session[:split_item_id] = params[:receipt_item_id]
    redirect_to session[:return_to]
  end

  def create
    @receipt_item = ReceiptItem.find params[:receipt_item_id]
    @receipt_item_split = @receipt_item.receipt_item_splits.new params[:receipt_item_split]
    @receipt_item_split.save
    session[:is_splitting] = false
    session[:split_expense] = nil
    session[:split_coach_id]

    # raise @receipt_item.receipt.has_non_split_items.to_yaml


    if @receipt_item.receipt.has_non_split_items
      # still need more split
      redirect_to new_receipt_item_receipt_item_split_url(@receipt_item.receipt.next_non_split_item)
    else
      # done with this receipt

      @receipt_item.has_split = true
      @receipt_item.save

      # nsr = NonSplitReceipt.where(receipt_id: @receipt_item.receipt.id).first
      # if !nsr.nil?
      #   nsr.is_processed = true
      #   nsr.save
      # end

      redirect_to session[:return_to]
    end
  end

  def edit
    @split = ReceiptItemSplit.find params[:id]
    @split.is_void = true
    @split.save

    session[:split_coach_id] = @split.coach_id

    redirect_to(new_receipt_item_receipt_item_split_url(@split.receipt_item))
  end

  def destroy
    @receipt_item = ReceiptItem.find params[:receipt_item_id]
    @receipt_item.receipt_item_splits.each do |split|
      split.is_void = true
      split.save
    end

    respond_to do |format|
      format.html { redirect_to receipt_items_url }
      format.json { head :no_content }
    end
  end
end
