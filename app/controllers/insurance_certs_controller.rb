class InsuranceCertsController < StudentApplicationController

  before_filter :find_insurance_from_params_id

  # GET /insurance_certs
  # GET /insurance_certs.json
  def index
    @insurance_certs = InsuranceCert.all

    respond_to do |format|
      format.html # index.html.erb
      format.json { render json: @insurance_certs }
    end
  end

  # GET /insurance_certs/1
  # GET /insurance_certs/1.json
  def show
    @insurance_cert = InsuranceCert.find(params[:id])

    respond_to do |format|
      format.html # show.html.erb
      format.json { render json: @insurance_cert }
    end
  end

  def preview
    @insurance_cert = InsuranceCert.find(params[:id])
    respond_to do |format|
      format.html { render :layout => 'plain' }
      format.json { render json: @insurance_cert }
    end
  end

  def print
    @insurance_cert = InsuranceCert.find(params[:id])
    respond_to do |format|
      format.html { render :layout => false}
      format.json { render json: @insurance_cert }
    end
  end

  # GET /insurance_certs/new
  # GET /insurance_certs/new.json
  def new
    @insurance_cert = InsuranceCert.new

    respond_to do |format|
      format.html # new.html.erb
      format.json { render json: @insurance_cert }
    end
  end

  # GET /insurance_certs/1/edit
  def edit
    @insurance_cert = InsuranceCert.find(params[:id])
  end

  # POST /insurance_certs
  # POST /insurance_certs.json
  def create
    @insurance_cert = InsuranceCert.new(params[:insurance_cert])

    @insurance_cert.insurance = @insurance
    @insurance.insurance_cert = @insurance_cert
    @insurance.save!

    respond_to do |format|
      if @insurance_cert.save
        format.html { redirect_to student_url(@student, anchor:'forms'), notice: 'Insurance cert was successfully created.' }
        format.json { render json: @insurance_cert, status: :created, location: @insurance_cert }
      else
        format.html { render action: "new" }
        format.json { render json: @insurance_cert.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /insurance_certs/1
  # PUT /insurance_certs/1.json
  def update
    @insurance_cert = InsuranceCert.find(params[:id])

    respond_to do |format|
      if @insurance_cert.update_attributes(params[:insurance_cert])
        format.html { redirect_to student_url(@student, anchor:'forms'), notice: 'Insurance cert was successfully updated.' }
        format.json { head :no_content }
      else
        format.html { render action: "edit" }
        format.json { render json: @insurance_cert.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /insurance_certs/1
  # DELETE /insurance_certs/1.json
  def destroy
    @insurance_cert = InsuranceCert.find(params[:id])
    @insurance_cert.destroy

    respond_to do |format|
      format.html { redirect_to student_url(@student, anchor:'forms') }
      format.json { head :no_content }
    end
  end

  protected
    def find_insurance_from_params_id
      @insurance = Insurance.find params[:insurance_id]
    end
end
