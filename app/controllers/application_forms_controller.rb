#encoding: utf-8
class ApplicationFormsController < StudentApplicationController

  # GET /application_forms
  # GET /application_forms.json
  def index
    @application_forms = @student.application_forms

    respond_to do |format|
      format.html # index.html.haml
      format.json { render json: @application_forms }
    end
  end

  # GET /application_forms/1
  # GET /application_forms/1.json
  def show
    @application_form = ApplicationForm.find(params[:id])

    respond_to do |format|
      format.html # show.html.haml
      format.json { render json: @application_form }
    end
  end

  def preview
    @application_form = ApplicationForm.find(params[:id])

    # backward: clone chinese address if nil
    if @application_form.chinese_address.nil?
      @application_form.chinese_address = @student.chinese_address
      @application_form.save
    end

    @language = @application_form.language
    @language = @application_form.student.language if @language.blank?

    respond_to do |format|
      format.html { render :layout => 'plain' }
      format.json { render json: @application_form }
    end
  end

  def print
    @application_form = ApplicationForm.find(params[:id])


    @language = @application_form.language
    @language = @application_form.student.language if @language.blank?

    respond_to do |format|
      format.html { render :layout => false}
      format.json { render json: @application_form }
    end
  end

  # GET /application_forms/new
  # GET /application_forms/new.json
  def new
    @application_form = ApplicationForm.new

    @failed_exams = @student.still_failed_exams

    @application_form.is_skip_theory ||= @student.need_skip_theory

    # raise @failed_exams.to_yaml

    respond_to do |format|
      format.html # new.html.haml
      format.json { render json: @application_form }
    end
  end

  # GET /application_forms/1/edit
  def edit
    @application_form = ApplicationForm.find(params[:id])
    @failed_exams = @student.still_failed_exams
  end

  # POST /application_forms
  # POST /application_forms.json
  def create
    @application_form = ApplicationForm.new(params[:application_form])
    @application_form.id_card = @student.id_card.gsub("(","").gsub(")","")
    @application_form.id_document = @student.id_card_kind
    @application_form.address = @student.address
    @application_form.chinese_address = @student.chinese_address
    @application_form.address_code = @student.street_no
    @application_form.tel = @student.tel
    @application_form.restriction = @student.vision
    @application_form.language = @student.language

    @application_form.is_400cc = true if [2,3].include?(@application_form.category_id) && @application_form.license_kind_id == 1

    if params[:reexam] && params[:exam] != nil
      exam = Exam.find params[:exam]
      @application_form.failed_or_absent_at = exam.exam_at_include_extend_form
      if exam.status == 'failed'
        @application_form.is_failed = true
      elsif exam.status == 'absent'
        @application_form.is_absent = true
      end
    end


    coach = Coach.find_by_coach_no @application_form.coach_no
    if coach
      @application_form.coach_name = coach.name
    end

    school = School.find_by_school_no @application_form.school_no
    @application_form.school_tel = "#{school.tel}, #{school.tel_alt}"
    @application_form.school = school

    @application_form.student = @student

    # create exam record
    exam = Exam.new
    exam.remark = ''
    exam.status = 'waiting'
    exam.student = @student
    exam.application_form = @application_form

    if params[:skip_theory] == "on"
      exam.exam_type = 'road'
      @application_form.is_skip_theory = true
    else
      exam.exam_type = (@student.has_passed_and_valid_theory_exam || (@application_form.license_kind.short_name=='普通' && @student.has_normal_license) || (@application_form.license_kind.short_name=='特別')) ? 'road' : 'theory'
    end

    # sepcial case: machine
    if @application_form.category.code == 'Machine'
      exam.exam_type = 'machine'
    end
    exam.save

    respond_to do |format|
      if @application_form.save
        format.html { redirect_to student_url(@student, anchor:'forms'), notice: 'Application form was successfully created.' }
        format.json { render json: @application_form, status: :created, location: @application_form }
      else
        format.html { render action: "new" }
        format.json { render json: @application_form.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /application_forms/1
  # PUT /application_forms/1.json
  def update
    @application_form = ApplicationForm.find(params[:id])

    respond_to do |format|
      if @application_form.update_attributes(params[:application_form])
        format.html { redirect_to student_url(@student, anchor:'forms'), notice: 'Application form was successfully updated.' }
        format.json { head :no_content }
      else
        format.html { render action: "edit" }
        format.json { render json: @application_form.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /application_forms/1
  # DELETE /application_forms/1.json
  def destroy
    @application_form = ApplicationForm.find(params[:id])
    # @application_form.destroy
    @application_form.is_deleted = true
    @application_form.deleted_at = DateTime.current
    @application_form.save!

    # remove all exams related to this applicaton form
    @application_form.exams.each do |exam|
      exam.is_deleted = true
      exam.deleted_at = DateTime.current
      exam.save!
    end

    respond_to do |format|
      format.html { redirect_to student_url(@student, anchor:'forms') }
      format.json { head :no_content }
    end
  end
end
