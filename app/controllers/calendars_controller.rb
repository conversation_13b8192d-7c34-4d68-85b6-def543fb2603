class CalendarsController < ApplicationController
  def index

    # Special case: ?year &month & day
    if !params[:day].blank?
      redirect_to "/calendars/#{params[:year]}-#{params[:month]}-#{params[:day]}"
      return
    end
    # end special case

    session[:calendar_school] = params[:school] unless params[:school].nil?
    params[:school] = session[:calendar_school] if params[:school].nil?

    @school = nil
    @school = School.friendly.find params[:school] unless params[:school].blank?

    params[:month] ||= Date.current.month
    params[:year] ||= Date.current.year

    CalendarEvent.destroy_all

    date = "#{params[:year]}-#{params[:month]}-01".to_date
    exams = Exam.where latest_exam_at: date..date.end_of_month+1
    exams.each do |e|
      # next if e.latest_exam_at.nil?
      next if e.student.nil?
      # next if e.latest_exam_at.month != params[:month].to_i
      # next if e.latest_exam_at.year != params[:year].to_i
      next if @school != nil && e.student.school != @school

      event = CalendarEvent.find_by_exam_type_and_start_time e.exam_type, e.latest_exam_at.to_date
      # raise event.to_yaml
      if event.nil?
        event = CalendarEvent.create start_time: e.latest_exam_at.to_date, count: 1, exam_type: e.exam_type
      else
        event.count += 1
      end
      event.save!
    end

    @events = CalendarEvent.all
  end

  def show
    session[:calendar_school] = params[:school] unless params[:school].nil?
    params[:school] = session[:calendar_school] if params[:school].nil?
    @school = nil
    @school = School.find params[:school] unless params[:school].blank?

    @date = params[:date]
    road_exams = Exam.where exam_type: 'road', latest_exam_at: params[:date].to_datetime..params[:date].to_datetime.end_of_day
    theory_exams = Exam.where exam_type: 'theory', latest_exam_at: params[:date].to_datetime..params[:date].to_datetime.end_of_day

    machine_exams = []
    if params[:date].to_date <= 2017-12-31
      @machine_exams = Exam.where exam_type: 'machine', latest_exam_at: params[:date].to_datetime..params[:date].to_datetime.end_of_day
    end

    if @school == nil
      @road_exams = road_exams
      @theory_exams = theory_exams
      @machine_exams = machine_exams
    else
      @road_exams = []
      road_exams.each do |e|
        next if @school != nil && e.student.school != @school
        @road_exams << e
      end

      @theory_exams = []
      theory_exams.each do |e|
        next if @school != nil && e.student.school != @school
        @theory_exams << e
      end

      @machine_exams = []
      machine_exams.each do |e|
        next if @school != nil && e.student.school != @school
        @machine_exams << e
      end
    end

    @road_exams = @road_exams.sort_by { |e| [e.application_form.school_id, e.application_form.category.sort, e.latest_exam_at] }
    @theory_exams = @theory_exams.sort_by { |e| [e.application_form.school_id, e.application_form.category.sort, e.latest_exam_at] }
    @machine_exams = @machine_exams.sort_by { |e| [e.application_form.school_id, e.application_form.category.sort, e.latest_exam_at] }
  end
end