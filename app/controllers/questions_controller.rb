class QuestionsController < InheritedResources::Base
  def index
    @theory_questions = Question.where(exam_type: 'theory').order('chapter, no')
    @machine_questions = Question.where(exam_type: 'machine').order('chapter, no')

    @questions = Question.all

    # respond_to do |format|
    #   format.html # index.html.erb
    #   format.json { render json: {theory: @theory_questions, machine: @machine_questions} }
    # end

  end

  def bulk_list
    @questions = Question.where('exam_type=? and chapter=?', 'theory', params[:chapter])
  end
  def bulk_list_en
    @questions = Question.where('exam_type=? and chapter=?', 'theory', params[:chapter])
  end
  def bulk_list_pt
    @questions = Question.where('exam_type=? and chapter=?', 'theory', params[:chapter])
  end

  def add_a1
    Question.all.each do |q|
      q.a1_zh = q.answer_zh
      q.a1_en = q.answer_en
      q.a1_pt = q.answer_pt
      q.save
    end
    raise "Done"
  end
end
