class NormalFormsController < StudentApplicationController
  # GET /normal_forms
  # GET /normal_forms.json
  def index
    @normal_forms = NormalForm.all

    respond_to do |format|
      format.html # index.html.erb
      format.json { render json: @normal_forms }
    end
  end

  # GET /normal_forms/1
  # GET /normal_forms/1.json
  def show
    @normal_form = NormalForm.find(params[:id])

    respond_to do |format|
      format.html # show.html.erb
      format.json { render json: @normal_form }
    end
  end

  def preview
    @normal_form = NormalForm.find(params[:id])

    # backward: clone chinese address if nil
    if @normal_form.chinese_address.nil?
      @normal_form.chinese_address = @student.chinese_address
      @normal_form.save
    end

    respond_to do |format|
      format.html { render :layout => 'plain' }
      format.json { render json: @normal_form }
    end
  end

  def print
    @normal_form = NormalForm.find(params[:id])
    respond_to do |format|
      format.html { render :layout => false}
      format.json { render json: @normal_form }
    end
  end

  # GET /normal_forms/new
  # GET /normal_forms/new.json
  def new
    @normal_form = NormalForm.new
    @normal_form.license = @student.licenses.first if @student.licenses.size > 0
    @normal_form.action = :revalidacao

    respond_to do |format|
      format.html # new.html.erb
      format.json { render json: @normal_form }
    end
  end

  # GET /normal_forms/1/edit
  def edit
    @normal_form = NormalForm.find(params[:id])
  end

  # POST /normal_forms
  # POST /normal_forms.json
  def create
    @normal_form = NormalForm.new(params[:normal_form])
    @normal_form.student = @student
    @normal_form.id_card = @student.id_card
    @normal_form.id_card_kind = @student.id_card_kind
    @normal_form.tel = @student.tel
    @normal_form.address = @student.address
    @normal_form.address2 = @student.address2
    if @normal_form.license
      @normal_form.license_no = @normal_form.license.no
    end

    respond_to do |format|
      if @normal_form.save
        format.html { redirect_to student_url(@student, anchor:'forms'), notice: 'Normal form was successfully created.' }
        format.json { render json: @normal_form, status: :created, location: @normal_form }
      else
        format.html { render action: "new" }
        format.json { render json: @normal_form.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /normal_forms/1
  # PUT /normal_forms/1.json
  def update
    @normal_form = NormalForm.find(params[:id])

    respond_to do |format|
      if @normal_form.update_attributes(params[:normal_form])
        format.html { redirect_to student_url(@student, anchor:'forms'), notice: 'Normal form was successfully updated.' }
        format.json { head :no_content }
      else
        format.html { render action: "edit" }
        format.json { render json: @normal_form.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /normal_forms/1
  # DELETE /normal_forms/1.json
  def destroy
    @normal_form = NormalForm.find(params[:id])
    # @normal_form.destroy
    @normal_form.is_deleted = true
    @normal_form.deleted_at = DateTime.current
    @normal_form.save!

    respond_to do |format|
      format.html { redirect_to student_url(@student, anchor:'forms') }
      format.json { head :no_content }
    end
  end
end
