# encoding: utf-8
class ExtendFormsController < StudentApplicationController
  # GET /extend_forms
  # GET /extend_forms.json
  def index
    @extend_forms = ExtendForm.all

    respond_to do |format|
      format.html # index.html.erb
      format.json { render json: @extend_forms }
    end
  end

  # GET /extend_forms/1
  # GET /extend_forms/1.json
  def show
    @extend_form = ExtendForm.find(params[:id])

    respond_to do |format|
      format.html # show.html.erb
      format.json { render json: @extend_form }
    end
  end

  def preview
    @extend_form = ExtendForm.find(params[:id])

    respond_to do |format|
      format.html { render :layout => 'plain' }
      format.json { render json: @extend_form }
    end
  end

  def print
    @extend_form = ExtendForm.find(params[:id])
    respond_to do |format|
      format.html { render :layout => false}
      format.json { render json: @extend_form }
    end
  end

  # GET /extend_forms/new
  # GET /extend_forms/new.json
  def new
    @extend_form = ExtendForm.new
    @extend_form.exam = @student.waiting_exams.first # TODO 2025-07 應改為 .last 預設攞最後一個。
    @extend_form.action = :adiamento
    @extend_form.is_2020_form = true

    respond_to do |format|
      format.html # new.html.erb
      format.json { render json: @extend_form }
    end
  end

  # GET /extend_forms/1/edit
  def edit
    @extend_form = ExtendForm.find(params[:id])
  end

  def edit_new_exam
    @extend_form = ExtendForm.find params[:extend_form_id]
  end

  # POST /extend_forms
  # POST /extend_forms.json
  def create
    @extend_form = ExtendForm.new(params[:extend_form])
    @extend_form.student = @student
    @extend_form.is_2020_form = true
    @extend_form.id_card = @student.id_card

    respond_to do |format|
      if @extend_form.save
        format.html { redirect_to student_url(@student, anchor:'forms'), notice: 'Extend form was successfully created.' }
        format.json { render json: @extend_form, status: :created, location: @extend_form }
      else
        format.html { render action: "new" }
        format.json { render json: @extend_form.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /extend_forms/1
  # PUT /extend_forms/1.json
  def update
    @extend_form = ExtendForm.find(params[:id])

    if params[:exam_time]
      exam_time = params[:exam_time]
      hour = 0
      min = 0
      if exam_time.length == 4
        hour = exam_time[0..1].to_i
        min = exam_time[2..3].to_i
      else
        hour = exam_time[0..1].to_i
        min = exam_time[3..4].to_i
      end
    end


    respond_to do |format|
      if @extend_form.update_attributes(params[:extend_form])
        # set new exam at date to extend form
        @extend_form.new_exam_at = @extend_form.new_exam_at.to_date + hour.hours + min.minutes
        @extend_form.save!

        # save a clone of the latest new exam at date to exam model (for fast access)
        @extend_form.exam.latest_exam_at = @extend_form.new_exam_at
        @extend_form.exam.save

        # send SMS for new exam date
        exam_type_wording_zh = "駕駛理論測驗"
        exam_type_wording_zh = "駕駛實習測驗" if @extend_form.exam.exam_type == "road"
        exam_type_wording_zh = "機器測驗" if @extend_form.exam.exam_type == "machine"

        exam_type_wording_en = "Theory Test"
        exam_type_wording_en = "Road Test" if @extend_form.exam.exam_type == "road"
        exam_type_wording_en = "Mechanic Test" if @extend_form.exam.exam_type == "machine"

        create_sms_record(
          @extend_form.exam.student,
          "#{@extend_form.exam.student.school.name_zh} #{@extend_form.exam.student.chinese_name} #{@extend_form.exam.category.upcase} #{exam_type_wording_zh} 考試編號:#{@extend_form.exam.exam_code} 日期:#{@extend_form.exam.latest_exam_at.to_date} 時間:#{l @extend_form.exam.latest_exam_at, format: :time} 僅供參考",
          "#{@extend_form.exam.student.school.short_eng_name} #{@extend_form.exam.student.eng_name} #{@extend_form.exam.category.upcase} #{exam_type_wording_en} Test No:#{@extend_form.exam.exam_code} Date:#{@extend_form.exam.latest_exam_at.to_date} Time:#{l @extend_form.exam.latest_exam_at, format: :time} For Reference"
        )

        # render
        format.html { redirect_to student_url(@student, anchor:'forms'), notice: 'Extend form was successfully updated.' }
        format.json { head :no_content }
      else
        format.html { render action: "edit" }
        format.json { render json: @extend_form.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /extend_forms/1
  # DELETE /extend_forms/1.json
  def destroy
    @extend_form = ExtendForm.find(params[:id])
    # @extend_form.destroy
    @extend_form.is_deleted = true
    @extend_form.deleted_at = DateTime.current
    @extend_form.save!

    if !@extend_form.exam.nil?
      @extend_form.exam.update_latest_exam_at
    end

    respond_to do |format|
      format.html { redirect_to student_url(@student, anchor:'forms') }
      format.json { head :no_content }
    end
  end
end
