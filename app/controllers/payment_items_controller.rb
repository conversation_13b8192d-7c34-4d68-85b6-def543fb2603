class PaymentItemsController < ApplicationController
  # GET /payment_items
  # GET /payment_items.json
  def index
    @payment_items = PaymentItem.all

    respond_to do |format|
      format.html # index.html.haml
      format.json { render json: @payment_items }
    end
  end

  # GET /payment_items/1
  # GET /payment_items/1.json
  def show
    @payment_item = PaymentItem.find(params[:id])

    respond_to do |format|
      format.html # show.html.haml
      format.json { render json: @payment_item }
    end
  end

  # GET /payment_items/new
  # GET /payment_items/new.json
  def new
    @payment_item = PaymentItem.new

    respond_to do |format|
      format.html # new.html.haml
      format.json { render json: @payment_item }
    end
  end

  # GET /payment_items/1/edit
  def edit
    @payment_item = PaymentItem.find(params[:id])
  end

  # POST /payment_items
  # POST /payment_items.json
  def create
    @payment_item = PaymentItem.new(params[:payment_item])

    respond_to do |format|
      if @payment_item.save
        format.html { redirect_to @payment_item, notice: 'Payment item was successfully created.' }
        format.json { render json: @payment_item, status: :created, location: @payment_item }
      else
        format.html { render action: "new" }
        format.json { render json: @payment_item.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /payment_items/1
  # PUT /payment_items/1.json
  def update
    @payment_item = PaymentItem.find(params[:id])

    respond_to do |format|
      if @payment_item.update_attributes(params[:payment_item])
        format.html { redirect_to @payment_item, notice: 'Payment item was successfully updated.' }
        format.json { head :no_content }
      else
        format.html { render action: "edit" }
        format.json { render json: @payment_item.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /payment_items/1
  # DELETE /payment_items/1.json
  def destroy
    @payment_item = PaymentItem.find(params[:id])
    @payment_item.destroy

    respond_to do |format|
      format.html { redirect_to payment_items_url }
      format.json { head :no_content }
    end
  end
end
