#encoding: utf-8
class InsurancesController < ApplicationController
  # GET /insurances
  # GET /insurances.json
  def index
    # @insurances = Insurance.order('id desc').paginate(page: params[:page])
    @insurance_certs = InsuranceCert.order('created_at desc').paginate(page: params[:page])

    respond_to do |format|
      format.html # index.html.erb
      format.json { render json: @insurances }
    end
  end

  # GET /insurances/1
  # GET /insurances/1.json
  def show
    @student = Student.find params[:student_id]
    @insurance = Insurance.find(params[:id])

    respond_to do |format|
      format.html # show.html.erb
      format.json { render json: @insurance }
    end
  end

  def preview
    @student = Student.find params[:student_id]
    @insurance = Insurance.find(params[:id])
    respond_to do |format|
      format.html { render :layout => 'plain' }
      format.json { render json: @insurance }
    end
  end

  def print
    @student = Student.find params[:student_id]
    @insurance = Insurance.find(params[:id])
    respond_to do |format|
      format.html { render :layout => false}
      format.json { render json: @insurance }
    end
  end

  # GET /insurances/new
  # GET /insurances/new.json
  def new
    @student = Student.find params[:student_id]
    school_name = "ESCOLA DE CONDUCAO CHUNG WA   中華汽車駕駛學校"
    school_id = School.find_by_school_no(19).id

    # From 2019-09-04, all insurance are from ChungWa.
    # if @student.school.id == 7
    #   school_name = "ESCOLA DE CONDUCAO SENG HENG     勝興駕駛學校"
    #   school_id = @student.school.id
    # end

    @insurance = Insurance.new ({
      student_id: @student.id,
      school_id: school_id,
      id_card: @student.id_card,
      occupation: @student.occupation,
      address: @student.address,
      address2: @student.address2,
      school_name: school_name
    })
    @insurance.save!

    respond_to do |format|
      format.html # { redirect_to student_url(@student, anchor:'forms'), notice: 'Insurance was successfully created.' }
      format.json { render json: @insurance }
    end
  end

  # GET /insurances/1/edit
  def edit
    @student = Student.find params[:student_id]
    @insurance = Insurance.find(params[:id])
  end

  # POST /insurances
  # POST /insurances.json
  def create
    @student = Student.find params[:student_id]
    @insurance = Insurance.new(params[:insurance])

    respond_to do |format|
      if @insurance.save
        format.html { redirect_to student_url(@student, anchor:'forms'), notice: 'Insurance was successfully created.' }
        format.json { render json: @insurance, status: :created, location: @insurance }
      else
        format.html { render action: "new" }
        format.json { render json: @insurance.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /insurances/1
  # PUT /insurances/1.json
  def update
    @student = Student.find params[:student_id]
    @insurance = Insurance.find(params[:id])

    respond_to do |format|
      if @insurance.update_attributes(params[:insurance])
        format.html { redirect_to @insurance, notice: 'Insurance was successfully updated.' }
        format.json { head :no_content }
      else
        format.html { render action: "edit" }
        format.json { render json: @insurance.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /insurances/1
  # DELETE /insurances/1.json
  def destroy
    @student = Student.find params[:student_id]
    @insurance = Insurance.find(params[:id])
    # @insurance.destroy
    @insurance.is_deleted = true
    @insurance.deleted_at = DateTime.current
    @insurance.save!

    respond_to do |format|
      format.html { redirect_to student_url(@student, anchor:'forms') }
      format.json { head :no_content }
    end
  end
end
