class DsejFormsController < InheritedResources::Base

  def index
    redirect_to root_url
  end

  def show
    @dsej_form = DsejForm.find params[:id]
    redirect_to @dsej_form.student
  end

  def new
    @student = Student.find params[:student_id]
    @dsej_form = @student.dsej_forms.new
    # @dsej_form.school = @student.school # 2019-07-02: Disable pre-select school.

    # last_exam = @student.exams.last
    # if !last_exam.nil?
    #   @dsej_form.exam_at = last_exam.exam_at
    # end

    last_receipt = @student.receipts.last
    unless last_receipt.nil?
      if last_receipt.is_dsej?
        @dsej_form.course_number = last_receipt.dsej_course_number
        @dsej_form.course_name = last_receipt.dsej_course_name
      end
    end
  end

  def create
    # @student = Student.find params[:student_id]
    @dsej_form = DsejForm.new(params[:dsej_form])
    @dsej_form.start_at = @dsej_form.start_at.beginning_of_month unless @dsej_form.start_at.nil?
    @dsej_form.end_at = (Date.current.beginning_of_month + params[:months].to_i.month-3.days).end_of_month

    respond_to do |format|
      if @dsej_form.save
        format.html { redirect_to dsej_form_preview_url(@dsej_form.student, @dsej_form) }
        format.json { render json: @dsej_form, status: :created, location: @dsej_form }
      else
        format.html { render action: "new" }
        format.json { render json: @dsej_form.errors, status: :unprocessable_entity }
      end
    end
  end

  def edit
    @dsej_form = DsejForm.find params[:id]
    @student = @dsej_form.student
  end

  def destroy
    @dsej_form = DsejForm.find(params[:id])
    @student = @dsej_form.student

    @dsej_form.destroy

    respond_to do |format|
      format.html { redirect_to student_url(@student, anchor:'forms') }
      format.json { head :no_content }
    end
  end

  def preview
    @student = Student.find params[:student_id]
    @dsej_form = @student.dsej_forms.find params[:id]

    respond_to do |format|
      format.html { render :layout => false }
      format.json { render json: @receipt }
    end
  end

  def print
    @student = Student.find params[:student_id]
    @dsej_form = @student.dsej_forms.find params[:id]

    respond_to do |format|
      format.html { render :layout => false}
      format.json { render json: @receipt }
    end
  end



end
