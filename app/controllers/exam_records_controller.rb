# encoding: utf-8
class ExamRecordsController < InheritedResources::Base
  def index
    # @categories = ['新 theory', '新 cicl', '新 a1-125', '新 a1-150', '新 a2-650', '新 b', '新 c', '新 d1', '新 d2', '新 e', '新 machine', '覆 theory', '覆 cicl', '覆 a1-125', '覆 a1-150', '覆 a2-650', '覆 b', '覆 c', '覆 d1', '覆 d2', '覆 e', '覆 machine']
    @categories = [
        '新理論',
        '新A',
        '新AA',
        '新A1',
        '新A2',
        '新B',
        '新C',
        '新D1',
        '新D2',
        '新E',
        '覆理論',
        '覆A',
        '覆AA',
        '覆A1',
        '覆A2',
        '覆B',
        '覆C',
        '覆D1',
        '覆D2',
        '覆E']
  end

  def show

    @categories = {}
    Category.all.each do |c|
      @categories[c.id] = c
    end

    @exams = Exam.where(is_deleted:false)

    # @exam_records = ExamRecord.order('exam_scheduled_at, exam_time, handled_at, student_number').where(is_delete:false)

    # unless params[:school_no].nil?
      # @exam_records = @exam_records.where(school_no: params[:school_no])
    # end

    # @license_kind = nil
    # unless params[:license_kind_id].nil?
    #   @exam_records = @exam_records.where(license_kind_id: params[:license_kind_id])
    #   @license_kind = LicenseKind.where(id: params[:license_kind_id]).first
    # end


    if !params[:school_no].blank?
      @school = School.find(params[:school_no])
    end

    unless params[:exam_type].nil?

      exam_types = params[:exam_type].split(",")

      @exams = ExamRecord.or_where(exam_types[0])
      exam_types.each do |exam_type|
        @exams = @exams.or(ExamRecord.or_where(exam_type))
      end

    end

    @extend_forms = []

    params[:year] = Date.current.year if params[:year].blank?
    params[:month] = Date.current.month.to_s if params[:month].blank?


    @year = params[:year].to_i
    if params[:month].nil? || params[:month].empty?
      d = DateTime.new(@year)
      @exams = @exams.where(exam_at: d.beginning_of_year..d.end_of_year).or(@exams.where(latest_exam_at: d.beginning_of_year..d.end_of_year))
      @extend_forms = ExtendForm.where(is_deleted: false, new_exam_at: d.beginning_of_year..d.end_of_year)
    else
      @month = params[:month].to_i
      d = DateTime.new(@year, @month)
      @exams = @exams.where(exam_at: d.beginning_of_month..d.end_of_month).or(@exams.where(latest_exam_at: d.beginning_of_month..d.end_of_month))
      @extend_forms = ExtendForm.where(is_deleted: false, new_exam_at: d.beginning_of_month..d.end_of_month)
    end

    @next_month = d+1.month
    @prev_month = d-1.month


    @exams = @exams.order(:latest_exam_at)

    if !@school.nil?
      @exams = @exams.select do |e|
        e.student.school_id == params[:school_no].to_i
      end
    end

    # 2022-12-02: no need to distingush special license or normal license
    # @exams = @exams.select do |e|
    #   e.application_form.license_kind_id == params[:license_kind_id].to_i
    # end



    # Create the pre_exam_at, exam_at, new_exam_at
    exams = []
    @exams.each do |exam|
      pre_exam_at = nil
      exam_at = exam.exam_at
      new_exam_at = nil

      # 有改期
      if exam.extend_forms.count > 0
        if exam.exam_at == exam.latest_exam_at
          if exam.extend_forms.last.action == "antecipacao" # 提前
            new_exam_at = "改早"
          else
            new_exam_at = "改延"
          end
        else
          # Both date is this month
          if (exam.exam_at.year == d.year && exam.exam_at.month == d.month) && (exam.latest_exam_at.year == d.year && exam.latest_exam_at.month == d.month)
            pfda = exam.status[0].upcase
            pfda = "D" if exam_at.to_date.to_s != exam.latest_exam_at.to_date.to_s # TODO-2025-08 樊寶儀 / Deng Wai Heng 會出錯

            exams << {
                application_form: exam.application_form,
                next_application_form: exam.application_form.next_application_form,
                student: exam.student,
                coach: exam.coach,
                exam_type: exam.exam_type,
                vehicle: exam.vehicle,
                gov_handling_date: exam.gov_handling_date,
                status: exam.status[0].upcase,
                pfda: pfda,
                exam_at: exam.latest_exam_at.to_date,
                exam_at_time: exam.latest_exam_at.strftime("%H:%M"),
                pre_exam_at: "原" + exam.exam_at.to_date.to_s,
                new_exam_at: nil,
                latest_exam_at: exam.latest_exam_at,
                is_retake: exam.is_retake
            }
            if exam.extend_forms.last.action == "antecipacao" # 提前
              new_exam_at = "早至" + exam.latest_exam_at.to_date.to_s
            else
              new_exam_at = "延至" + exam.latest_exam_at.to_date.to_s
            end
            exams << {
                application_form: exam.application_form,
                next_application_form: exam.application_form.next_application_form,
                student: exam.student,
                coach: exam.coach,
                exam_type: exam.exam_type,
                vehicle: exam.vehicle,
                gov_handling_date: exam.gov_handling_date,
                status: exam.status[0].upcase,
                pfda: pfda,
                exam_at: exam_at.to_date,
                exam_at_time: exam_at.strftime("%H:%M"),
                pre_exam_at: nil,
                new_exam_at: new_exam_at,
                latest_exam_at: exam.latest_exam_at,
                is_retake: exam.is_retake
            }
            next
          elsif exam.latest_exam_at.year == d.year && exam.latest_exam_at.month == d.month
            # extended to this month
            pre_exam_at = "原" + exam.exam_at.to_date.to_s
            exam_at = exam.latest_exam_at
          else
            exam_at = exam.exam_at
            if exam.extend_forms.last.action == "antecipacao" # 提前
              new_exam_at = "早至" + exam.latest_exam_at.to_date.to_s
            else
              new_exam_at = "延至" + exam.latest_exam_at.to_date.to_s
            end
          end
        end
      end

      pfda = exam.status[0].upcase
      pfda = "D" if exam_at.to_date.to_s != exam.latest_exam_at.to_date.to_s

      exams << {
          application_form: exam.application_form,
          next_application_form: exam.application_form.next_application_form,
          student: exam.student,
          coach: exam.coach,
          exam_type: exam.exam_type,
          vehicle: exam.vehicle,
          gov_handling_date: exam.gov_handling_date,
          status: exam.status[0].upcase,
          pfda: pfda,
          exam_at: exam_at.to_date,
          exam_at_time: exam_at.strftime("%H:%M"),
          pre_exam_at: pre_exam_at,
          new_exam_at: new_exam_at,
          latest_exam_at: exam.latest_exam_at,
          is_retake: exam.is_retake,
          debug: exam.exam_at.to_date.to_s
      }

    end

    @exams = exams.sort_by { |e| e[:exam_at] }

    @count = @exams.count

    @count_special = 0
    @exams.each do |e|
      @count_special += 1 if e[:application_form].license_kind.exam_record_text == "特"
    end

    @title = ''
    if @school.nil?
      @title += '全部學校'
    else
      @title += @school.name_zh
    end

    @title += @license_kind.short_name unless @license_kind.nil?
    @title += '考期簿'
    @title += ExamRecord.category_nick_name(params[:exam_type]) unless params[:exam_type].nil?
    @title += " at #{@year}" unless @year.nil?
    @title += "— #{@month}" unless @month.nil?


    respond_to do |format|
      format.html{
        if params[:print].nil?
          @exams = @exams.paginate(page: params[:page], per_page: 100)
          render layout: 'plain'
        else # print does not paginate.
          render layout: 'print'
        end
      }
      format.xls{}
    end
  end
end
