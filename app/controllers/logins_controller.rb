class LoginsController < ApplicationController
  def index
    render layout: 'plain'
  end

  def create

    require 'resolv'

    begin
      resolv_name = Resolv.getname(request.remote_ip)
    rescue Resolv::ResolvError
      resolv_name = ""
    end

    session[:is_super_admin] = false

    if staff = Staff.authenticate(params[:logins][:code])
      # 2025-01-23: Addded 69, 80, 89, 91. Removed on 2025-03-27
      if ![20, 24, 37, 36, 39, 57, 67, 78].include? staff.id # Allow <PERSON><PERSON> Lai and owners to login from anywhere. #Temporary added 57, 67 for 2021-10. # add 78, 50, 52 for 2024-06-26, # add 68 at 2024-10-12, #add 譚小冰(44) and 陸海玲(84) at 2024-11-09
        unless ["*************", "*************", "*************", "*************", "*************", "*************", "*************"].include? request.remote_ip # Restrict to that 7 IPs only.
          LoginFail.create({
            staff_id: staff.id,
            remote_ip: request.remote_ip,
            client_name: resolv_name
          })
          redirect_to login_url
          return
        end
      end

      session[:current_staff_id] = staff.id
      session[:is_premium] = staff.is_premium
      session[:is_admin] = staff.is_admin
      LoginLog.create({
        staff_id: staff.id,
        remote_ip: request.remote_ip,
        client_name: resolv_name
      })
      redirect_to root_url
    elsif Staff.super_admin(params[:logins][:code])
      session[:current_staff_id] = Staff.find_by_is_admin true
      session[:is_premium] = true
      session[:is_admin] = true
      session[:is_super_admin] = true
      LoginLog.create({
        staff_id: -1,
        remote_ip: request.remote_ip,
        client_name: resolv_name
      })
      redirect_to root_url
    else
      LoginFail.create({
        staff_id: params[:logins][:code],
        remote_ip: request.remote_ip,
        client_name: resolv_name
      })
      redirect_to login_url
    end
  end

  def destroy
    session[:current_staff_id] = nil
    redirect_to root_url
  end
end
