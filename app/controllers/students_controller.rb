# encoding: utf-8
class StudentsController < ApplicationController
  def check_unique
    @students = Student.where(id_card: params[:id_card])
    @students |= Student.where(id_card_raw: params[:id_card])
    render json: { id_card: params[:id_card], count: @students.size }
  end

  # GET /students
  # GET /students.json
  def index
    @students = []
    return if params[:q].blank?

    unless params[:search].blank?
      e = Exam.where(exam_code: params[:q]).first

      redirect_to e.student
      return

    else
      @search_query = params[:q][:chinese_name_or_first_name_or_last_name_or_id_card_or_id_card_raw_or_tel_cont]

      has_query = false
      params[:q].each do |key, value|
        has_query = true unless value.blank?
      end

      unless has_query
        redirect_to("/students/advanced-search/")
        return
      end

      @students = @q.result

      # @search_query = ""

      # raise @students.inspect

      if @search_query[0..1] == "介：" || @search_query[0..1] == "介:"
        # @q = Student.ransack({:referrer => @search_query[2..999] })
        @students = Student.where("referrer LIKE ?", '%' + @search_query[2..999].strip + '%')
      end

      is_birthday = false
      if @search_query[0..1] == "生：" || @search_query[0..1] == "生:"
        # @q = Student.ransack({:referrer => @search_query[2..999] })
        @students.concat Student.where(birthday: @search_query.to_date)
        is_birthday = true
      end


      is_search_a_date = false
      begin
        unless @search_query.blank?
          # TODO: search only when date is in past.
          @search_query_date = @search_query.to_date
          is_search_a_date = true

          if @search_query_date.year >= 2012 && @search_query_date.year <= Date.current.year+3
            if !is_birthday
              # Redirect to Calendar
              redirect_to "/calendars/#{@search_query_date}"
              return
            end
          end
        end

      rescue # @search_query is not a date
        #do something if invalid
      end

      begin
        unless @search_query.blank?
          # search via exam_at
          exams = Exam.where exam_at: @search_query.to_datetime..@search_query.to_datetime.end_of_day
          exams.each do |e|
            @students << e.student unless e.student.nil?
          end
          extended_exams = ExtendForm.where new_exam_at: @search_query.to_datetime..@search_query.to_datetime.end_of_day
          extended_exams.each do |e|
            @students << e.student unless e.student.nil?
          end
        end
      rescue
        #do something if invalid
      end
    end



    @students = @students.sort do |a, b|
      a.school.id <=> b.school.id
    end

    if @students.size == 1 && @search_query == @students[0].id_card_raw
      redirect_to student_path(@students[0])
      return
    end

    respond_to do |format|
      format.html # index.html.haml
      format.json { render json: @students }
    end
  end

  def advanced_search

  end

  # GET /students/1
  # GET /students/1.json
  def show
    @student = Student.find(params[:id])

    @licenses = @student.licenses
    @exams = @student.exams.order("created_at")
    @application_forms = @student.application_forms.order("created_at")
    @health_forms = @student.health_forms.order("id DESC")
    @receipts = @student.receipts.order("id DESC")

    @remark = Remark.new

unless @student.remark.blank?
    @remark_sections = @student.remark.gsub("\r","").split("\n\n").map do |section|
      section.strip.lines.first
    end
end

    @uploaded_file = @student.uploaded_files.new

    respond_to do |format|
      format.html # show.html.haml
      format.json { render json: @student }
    end
  end

  def versions
    @student = Student.find(params[:student_id])
  end

  def print
    @student = Student.find(params[:student_id])

    @licenses = @student.licenses
    @exams = @student.exams
    @application_forms = @student.application_forms.order("id DESC")
    @health_forms = @student.health_forms.order("id DESC")

    respond_to do |format|
      format.html { render layout: 'print'}
      format.json { render json: @student }
    end
  end

  # GET /students/new
  # GET /students/new.json
  def new
    @student = Student.new

    @student.id_card = params[:id_card]

    if @student.id_card && @student.id_card.size == 8
      @student.id_card_kind = "BM"
    end
    if @student.id_card && @student.id_card[0] == 'W'
      @student.id_card_kind = '通行証'
    end


    respond_to do |format|
      format.html # new.html.haml
      format.json { render json: @student }
    end
  end

  # GET /students/1/edit
  def edit
    @student = Student.find(params[:id])
  end

  def edit_remark
    @student = Student.find params[:student_id]
  end

  def update_remark

  end

  def cache_waiting_result_students
    Student.cache_waiting_result_students!
    @count = WaitingResultStudent.count
    render layout:false
  end

  # POST /students
  # POST /students.json
  def create
    @student = Student.new(params[:student])

    @student.id_card_raw = @student.id_card.gsub(/\(\)\//, "")

    # Add () to HK and BM
    if @student.id_card_kind == "HK" or @student.id_card_kind == "BM"
      # check if we already have )
      if @student.id_card[-3..-3] != "(" or @student.id_card[-1..-1]!=")"
        @student.id_card = "#{@student.id_card[0..-2]}(#{@student.id_card[-1..-1]})"
      end
    end

    @student.id_card.upcase!

    if @student.id_card_kind == "BM"
      @student.issue_place = "D.S.I."
    elsif @student.id_card_kind == "HK"
      @student.issue_place = "Hong Kong"
    elsif @student.id_card_kind == "通行証"
      @student.issue_place = "China"
    end

    respond_to do |format|
      if @student.save
        format.html { redirect_to @student, notice: 'Student was successfully created.' }
        format.json { render json: @student, status: :created, location: @student }
      else
        format.html { render action: "new" }
        format.json { render json: @student.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /students/1
  # PUT /students/1.json
  def update
    @student = Student.find(params[:id])

    respond_to do |format|
      if @student.update_attributes(params[:student])
        @student.id_card_raw = @student.id_card.gsub /[\(\)]/, ''
        @student.save!
        if params[:student][:remark]
          format.html { redirect_to student_url(@student, anchor:'remarks'), notice: '學生資料更新成功。' }
        else
          format.html { redirect_to @student, notice: '學生資料更新成功。' }
        end
        format.json { head :no_content }
      else
        format.html { render action: "edit" }
        format.json { render json: @student.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /students/1
  # DELETE /students/1.json
  def destroy
    @student = Student.find(params[:id])
    @student.destroy

    respond_to do |format|
      format.html { redirect_to students_url }
      format.json { head :no_content }
    end
  end

  def reset_password
    student = Student.find params[:id]
    hash = generate_alphabet_hash + generate_number_hash
    student.course_student.password = hash
    student.course_student.plain_password = hash
    if student.id_card_raw.nil?
      student.id_card_raw = student.id_card.downcase.gsub(/\(\)\//, "")
      student.course_student.id_card_raw = student.id_card_raw
      student.save
    end
    student.course_student.has_post_remote = false
    student.course_student.save

    redirect_to student_url(student, anchor:'exams')
  end

  def move_all_remarks
    raise "secret not correct" if params[:secret] != "thisismakzan"
    Student.all.each do |student|
      student.remark = ''
      student.remarks.each do |r|
        student.remark += r.content
        student.remark += "\n"
      end
      if student.save
      else
        # raise student.to_yaml
      end
    end
    raise "done"
  end

end
