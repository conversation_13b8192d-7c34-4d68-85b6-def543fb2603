# coding: UTF-8
class UploadedFilesController < ApplicationController
	before_filter :find_student_from_params_id

	def index		
		@uploaded_file = @student.uploaded_files.new
	end

	def show
		@student.uploaded_files.find (params[:file_id] || params[:id])
	end

	def new
		
	end

	def create		
		@uploaded_file = @student.uploaded_files.create params[:uploaded_file]

		respond_to do |format|
      if @uploaded_file.save
        format.html { redirect_to student_url(@uploaded_file.student, anchor:'files'), notice: '檔案已成功上傳' }
        format.json { render json: @uploaded_file, status: :created, location: @uploaded_file }
      else
        format.html { redirect_to student_url(@uploaded_file.student, anchor:'files'), alert: 'Failed: ' + @uploaded_file.errors.values.to_s }
        format.json { render json: @uploaded_file.errors, status: :unprocessable_entity }
      end
    end
	end
  
  def destroy
    @uploaded_file = @student.uploaded_files.find(params[:id])
    @uploaded_file.destroy

    respond_to do |format|
      format.html { redirect_to @student }
      format.json { head :no_content }
    end
  end


	protected
  def find_student_from_params_id
    @student = Student.find params[:student_id]
  end
end