class RemindersController < InheritedResources::Base

  def index
    # Special case: ?year &month & day
    # if !params[:day].blank?
    #   redirect_to "/calendars/#{params[:year]}-#{params[:month]}-#{params[:day]}"
    #   return
    # end
    # end special case

    session[:reminder_school] = params[:school] unless params[:school].nil?
    params[:school] = session[:calendar_school] if params[:school].nil?

    @school = nil
    @school = School.friendly.find params[:school] unless params[:school].blank?

    params[:month] ||= Date.current.month
    params[:year] ||= Date.current.year
    
    date = "#{params[:year]}-#{params[:month]}-01".to_date
    
    if @school.blank?
      @reminders = Reminder.where('remind_at >= ? and remind_at <= ?', date.beginning_of_month, date.end_of_month).order(:remind_at)
    else
      @reminders = Reminder.where(school: @school).where('remind_at >= ? and remind_at <= ?', date.beginning_of_month, date.end_of_month).order(:remind_at)
    end
  end
  
  def calendar
    session[:reminder_school] = params[:school] unless params[:school].nil?
    params[:school] = session[:calendar_school] if params[:school].nil?

    @school = nil
    @school = School.friendly.find params[:school] unless params[:school].blank?

    params[:month] ||= Date.current.month
    params[:year] ||= Date.current.year
    
    date = "#{params[:year]}-#{params[:month]}-01".to_date
    
    if @school.blank?
      @reminders = Reminder.where('remind_at >= ? and remind_at <= ?', date.beginning_of_month, date.end_of_month)
    else
      @reminders = Reminder.where(school: @school).where('remind_at >= ? and remind_at <= ?', date.beginning_of_month, date.end_of_month)
    end
  end
  

  def new
    @reminder = Reminder.new
    @reminder.student = Student.find params[:student_id]
    @reminder.school = @reminder.student.school
    @reminder.responsible_staff = current_staff
    @student = @reminder.student
  end

  def edit
    @reminder = Reminder.find params[:id]
    @student = @reminder.student
  end

  def show
    @reminder = Reminder.find params[:id]
    @student = @reminder.student
  end

  private

    def reminder_params
      params.require(:reminder).permit(:student_id, :school_id, :content, :remind_at, :responsible_staff)
    end
end

