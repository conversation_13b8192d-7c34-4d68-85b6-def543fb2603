class VehiclesController < ApplicationController
  # GET /vehicles
  # GET /vehicles.json
  def index

    school_id = params[:school_id]
    unless school_id.blank?
      @vehicles = Vehicle.not_is_deleted.where(school_id: school_id).order(:school_id, :cc, :license_no).page params[:page]
    else
      @vehicles = Vehicle.not_is_deleted.order(:school_id, :cc, :license_no).page params[:page]
    end

    @schools = School.all

    # @vehicles = Vehicle.not_is_deleted.order(:school_id, :cc, :license_no).page params[:page]

    respond_to do |format|
      format.html # index.html.haml
      format.json { render json: @vehicles }
    end
  end

  def trash
    @vehicles = Vehicle.is_deleted.order(:school_id, :cc, :license_no).page params[:page]

    respond_to do |format|
      format.html # index.html.haml
      format.json { render json: @vehicles }
    end
  end

  def recover
    @vehicle = Vehicle.find(params[:vehicle_id])
    @vehicle.is_deleted = false
    @vehicle.save
    redirect_to @vehicle
  end

  # GET /vehicles/1
  # GET /vehicles/1.json
  def show
    @vehicle = Vehicle.find(params[:id])

    respond_to do |format|
      format.html # show.html.haml
      format.json { render json: @vehicle }
    end
  end

  # GET /vehicles/new
  # GET /vehicles/new.json
  def new
    @vehicle = Vehicle.new

    respond_to do |format|
      format.html # new.html.haml
      format.json { render json: @vehicle }
    end
  end

  # GET /vehicles/1/edit
  def edit
    @vehicle = Vehicle.find(params[:id])
  end

  # POST /vehicles
  # POST /vehicles.json
  def create
    @vehicle = Vehicle.new(params[:vehicle])

    respond_to do |format|
      if @vehicle.save
        format.html { redirect_to @vehicle, notice: 'Vehicle was successfully created.' }
        format.json { render json: @vehicle, status: :created, location: @vehicle }
      else
        format.html { render action: "new" }
        format.json { render json: @vehicle.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /vehicles/1
  # PUT /vehicles/1.json
  def update
    @vehicle = Vehicle.find(params[:id])

    respond_to do |format|
      if @vehicle.update_attributes(params[:vehicle])
        format.html { redirect_to @vehicle, notice: 'Vehicle was successfully updated.' }
        format.json { head :no_content }
      else
        format.html { render action: "edit" }
        format.json { render json: @vehicle.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /vehicles/1
  # DELETE /vehicles/1.json
  def destroy
    @vehicle = Vehicle.find(params[:id])
    @vehicle.is_deleted = true
    @vehicle.deleted_at = DateTime.current
    @vehicle.save!

    respond_to do |format|
      format.html { redirect_to vehicles_url }
      format.json { head :no_content }
    end
  end
end
