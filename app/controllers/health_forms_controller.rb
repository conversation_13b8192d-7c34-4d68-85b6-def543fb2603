class HealthFormsController < StudentApplicationController
  # GET /health_forms
  # GET /health_forms.json
  def index

    redirect_to student_url(@student, anchor:'forms')
    return

    # @health_forms = @student.health_forms
    #
    # respond_to do |format|
    #   format.html # index.html.erb
    #   format.json { render json: @health_forms }
    # end
  end

  # GET /health_forms/1
  # GET /health_forms/1.json
  def show
    @health_form = HealthForm.find(params[:id])

    respond_to do |format|
      format.html # show.html.erb
      format.json { render json: @health_form }
    end
  end

  def preview
    @health_form = HealthForm.find(params[:id])

    respond_to do |format|
      format.html { render :layout => 'plain' }
      format.json { render json: @health_form }
    end
  end

  def print
    @health_form = HealthForm.find(params[:id])
    respond_to do |format|
      format.html { render :layout => false}
      format.json { render json: @health_form }
    end
  end

  # GET /health_forms/new
  # GET /health_forms/new.json
  def new
    @health_form = HealthForm.new

    # TODO: handle two types of licenses
    if @student.has_license
      @student.licenses.each do |license|
        @health_form.allow_cicl ||= license.allow_cicl
        @health_form.allow_a1 ||= license.allow_a1 || license.allow_a1_125 || license.allow_a1_150 || license.allow_a1_170 || license.allow_a1_200 || license.allow_a1_250
        @health_form.allow_a2 ||= license.allow_a2
        @health_form.allow_b ||= license.allow_b || license.allow_b_auto
        @health_form.allow_c ||= license.allow_c
        @health_form.allow_d1 ||= license.allow_d1
        @health_form.allow_d2 ||= license.allow_d2
        @health_form.allow_e ||= license.allow_e
      end
    end

    respond_to do |format|
      format.html # new.html.erb
      format.json { render json: @health_form }
    end
  end

  # GET /health_forms/1/edit
  def edit
    @health_form = HealthForm.find(params[:id])
  end

  # POST /health_forms
  # POST /health_forms.json
  def create
    @health_form = HealthForm.new(params[:health_form])

    @health_form.student = @student

    if @health_form.application_form
      application_form = @health_form.application_form
      category = application_form.category
      if category.code == 'CICL'
        @health_form.allow_cicl = true
      elsif category.code == 'A' && category.parameter == '1'
        @health_form.allow_a1 = true
      elsif category.code == 'A' && category.parameter == '2'
        @health_form.allow_a2 = true
      elsif category.code == 'B'
        @health_form.allow_b = true
      elsif category.code == 'C'
        @health_form.allow_c = true
      elsif category.code == 'D' && category.parameter == '1'
        @health_form.allow_d1 = true
      elsif category.code == 'D' && category.parameter == '2'
        @health_form.allow_d2 = true
      elsif category.code == 'E'
        @health_form.allow_e = true
      end
    end

    respond_to do |format|
      if @health_form.save
        format.html { redirect_to student_url(@student, anchor:'forms'), notice: 'Health form was successfully created.' }
        format.json { render json: @health_form, status: :created, location: @health_form }
      else
        format.html { render action: "new" }
        format.json { render json: @health_form.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /health_forms/1
  # PUT /health_forms/1.json
  def update
    @health_form = HealthForm.find(params[:id])

    respond_to do |format|
      if @health_form.update_attributes(params[:health_form])
        format.html { redirect_to student_url(@student, anchor:'forms'), notice: 'Health form was successfully updated.' }
        format.json { head :no_content }
      else
        format.html { render action: "edit" }
        format.json { render json: @health_form.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /health_forms/1
  # DELETE /health_forms/1.json
  def destroy
    @health_form = HealthForm.find(params[:id])
    # @health_form.destroy
    @health_form.is_deleted = true
    @health_form.deleted_at = DateTime.current
    @health_form.save!

    respond_to do |format|
      format.html { redirect_to student_url(@student, anchor:'forms') }
      format.json { head :no_content }
    end
  end
end
