class LicensesController < ApplicationController
  before_filter :find_student_from_params_id

  def index
    @licenses = @student.licenses

    respond_to do |format|
      format.html # index.html.haml
      format.json { render json: @licenses }
    end
  end

  def show
    @license = License.find(params[:id])

    respond_to do |format|
      format.html # show.html.haml
      format.json { render json: @license }
    end
  end


  def new
    @license = License.new

    respond_to do |format|
      format.html # new.html.haml
      format.json { render json: @license }
    end
  end


  def edit
    @license = License.find(params[:id])
  end


  def create
    @license = License.new(params[:license])

    @license.student = @student
    @license.staff = current_staff

    respond_to do |format|
      if @license.save
        format.html { redirect_to student_url(@student, anchor:'licenses'), notice: 'License was successfully created.' }
        format.json { render json: @license, status: :created, location: @license }
      else
        format.html { render action: "new" }
        format.json { render json: @license.errors, status: :unprocessable_entity }
      end
    end
  end


  def update
    @license = License.find(params[:id])

    respond_to do |format|
      if @license.update_attributes(params[:license])
        format.html { redirect_to student_url(@student, anchor:'licenses'), notice: 'License was successfully updated.' }
        format.json { head :no_content }
      else
        format.html { render action: "edit" }
        format.json { render json: @license.errors, status: :unprocessable_entity }
      end
    end
  end


  def destroy
    @license = License.find(params[:id])
    # @license.destroy
    @license.is_deleted = true
    @license.deleted_at = DateTime.current
    @license.save!

    respond_to do |format|
      format.html { redirect_to student_url(@student, anchor:'licenses') }
      format.json { head :no_content }
    end
  end

  protected
    def find_student_from_params_id
      @student = Student.find params[:student_id]
    end
end
