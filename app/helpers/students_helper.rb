# encoding: UTF-8
module <PERSON><PERSON><PERSON><PERSON>
  def exam_days_diff (exam)
    date = exam.exam_at_include_extend_form
    "#{exam.days_since_exam.abs}日" unless date.past?
    # ago_or_future = (date.past?) ? "前" : "後"
    # "#{exam.days_since_exam.abs}日#{ago_or_future}"
  end

  def time_diff_in_words (date)
    ago_or_future = (date.past?) ? "前" : "後"
    if date.past?
      "#{time_ago_in_words(date)}#{ago_or_future}"
    else
      "#{time_ago_in_words(date)}#{ago_or_future}"
    end
  end

  # def exam_day_text (exam)

  # end
end
