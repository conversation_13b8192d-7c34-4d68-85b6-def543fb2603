# Place all the behaviors and hooks related to the matching controller here.
# All this logic will automatically be available in application.js.
# You can use CoffeeScript in this file: http://jashkenas.github.com/coffee-script/

@chungwa = @chungwa || {}

@chungwa.application_form = {}

@chungwa.application_form.monitorForm = ->

  picker = $('#failed-or-absent-at')
  picker.hide()
  isFailedInput = $('#application_form_is_failed')
  isAbsentInput = $('#application_form_is_absent')
  refresh = ->
    needPicker = isFailedInput.is(':checked') or isAbsentInput.is(':checked')
    if needPicker then picker.show() else picker.hide()
  isFailedInput.change -> refresh()
  isAbsentInput.change -> refresh()

  picker.bind 'change', ->
    if picker.find('input').val() == ""
      console.log "AAAAAA"
      $('#err_fail_or_absent_at_empty').show()
    else
      console.log "BBBBBB"
      $('#err_fail_or_absent_at_empty').hdie()

  $('.hide-unless-reexam').hide()

  $('#past-exam').hide()
  $('#reexam').click ->
    if $('#reexam').is(':checked')
      $('#past-exam').show()
      $('.hide-unless-reexam').show()
    else
      $('#past-exam').hide()
      $('.hide-unless-reexam').hide()

@chungwa.application_form.validateForm = (formElm)->
  # hide those errors
  $("[id^=error_]").hide()

  validateForm = ->
    hasError = false

    elm = $('#error_license_kind_id')
    if $('input[name="application_form[license_kind_id]"]:checked').length == 0
      hasError = true
      elm.show()
    else
      elm.hide()

    elm = $('#error_category_empty')
    if $('#application_form_category_id').val() == ""
      hasError = true
      elm.show()
    else
      elm.hide()

    # elm = $('#error_language')
    # if $('input[name="application_form[language]"]:checked').length == 0
    #   hasError = true
    #   elm.show()
    # else
    #   elm.hide()
    return hasError
  $(formElm).find('input, select').change ->
    validateForm()
  $(formElm).submit ->
    hasError = validateForm()
    return false if hasError
    return true
