# Place all the behaviors and hooks related to the matching controller here.
# All this logic will automatically be available in application.js.
# You can use CoffeeScript in this file: http://jashkenas.github.com/coffee-script/
@chungwa = @chungwa || {}

@chungwa.receiptForm = {}

$ ->
  $('.template').hide()

itemIndex = 0
@chungwa.receiptForm.addItem = (description='', amount='', price='', expense=0)->
  itemIndex++
  itemHTML = $('#item-template').clone().removeClass('template')
  itemHTML.find('.item-description').attr('name', "item[#{itemIndex}][description]")
  itemHTML.find('.item-amount').attr('name', "item[#{itemIndex}][amount]")
  itemHTML.find('.item-price').attr('name', "item[#{itemIndex}][price]")
  itemHTML.find('.item-expense').attr('name', "item[#{itemIndex}][expense]")
  itemHTML.find('.item-sum').attr('name', "item[#{itemIndex}][sum]")

  if description != ''
    itemHTML.find('.item-description').val(description)
    # itemHTML.find("option[value*='#{description}'").attr('selected','')
  itemHTML.find('.item-amount').val(amount) if amount != ''
  itemHTML.find('.item-price').val(price) if price != ''
  itemHTML.find('.item-expense').val(expense)
  itemHTML.find('.item-sum').val(amount*price) if amount != '' and price != ''

  $(itemHTML).fadeIn().appendTo $('#items')

  @calculateSum($(itemHTML).find('.item-description'))
  @calculateTotalSum()

  return $(itemHTML)

@chungwa.receiptForm.addItemFromButton = ->
  item = @addItem()
  # console.log item
  # @unlock(item)

@chungwa.receiptForm.changeItemDescription = (elm) ->
  if $("#use-chinese").is(":checked")
    description = $(elm).val()
  else
    description = $(elm).val() + "\n" + $(elm).find('option:checked').attr("data-eng")

  $(elm).parent().parent().find('.item-description').val description
  $(elm).parent().parent().parent().parent().find('.item-amount').val $(elm).find('option:checked').data('amount')
  $(elm).parent().parent().parent().parent().find('.item-price').val $(elm).find('option:checked').data('price')
  $(elm).parent().parent().parent().parent().find('.item-expense').val $(elm).find('option:checked').data('expense')
  console.log $(elm).find('option:checked').data('expense')
  # console.log @
  @calculateSum(elm)

@chungwa.receiptForm.lock = (elm) ->
  $(elm).parent().parent().parent().parent().find('input, textarea').attr('readonly', 'readonly')
  $(elm).parent().parent().parent().parent().find('select').attr('disabled', '')
  $(elm).hide()
  $(elm).parent().parent().find('.remove').hide()
  $(elm).parent().parent().find('.unlock').show()
@chungwa.receiptForm.unlock = (elm) ->
  $(elm).parent().parent().parent().parent().find('input, textarea').removeAttr('readonly')
  $(elm).parent().parent().parent().parent().find('select').removeAttr('disabled')
  $(elm).hide()
  $(elm).parent().parent().find('.lock').show()
  $(elm).parent().parent().find('.remove').show()
  $('.item-sum').attr('readonly','')
@chungwa.receiptForm.removeItem = (elm) ->
  that = this
  $(elm).parent().parent().parent().parent().fadeOut 300, ->
    $(this).remove()
    that.calculateTotalSum()

@chungwa.receiptForm.changeTemplate = (elm) ->
  $('#items').empty()
  @calculateTotalSum()
  items = $(elm).find('option:checked').data('items')
  for item in items
    console.log item
    if $("#use-chinese").is(":checked")
      description = item.description
    else
      description = item.description + "\n" + item.description_en

    chungwa.receiptForm.addItem description, item.amount, item.price, item.expense
  # $('#items').find('input, select').attr('disabled','');
  $('.lock').each (i, elm)->
    console.log elm
    chungwa.receiptForm.lock(elm)

@chungwa.receiptForm.monitorForm = ->
  $('.template').hide()

  $('#receipt_school_id').change ->
    val = $('#receipt_school_id').val()
    switch val
      when '1' then $('#receipt-sample-prefix').text('CWM')
      when '2' then $('#receipt-sample-prefix').text('CWT')
      when '3' then $('#receipt-sample-prefix').text('CWG')
      when '4' then $('#receipt-sample-prefix').text('SSL')
      when '5' then $('#receipt-sample-prefix').text('SSV')
      when '6' then $('#receipt-sample-prefix').text('RCW')
      when '7' then $('#receipt-sample-prefix').text('SHN')

  # $("#use-chinese, #use-english").click ->
  #   changeAllDescriptionLanguage()


@chungwa.receiptForm.calculateSum = (elm) ->
  val1 = $(elm).parent().parent().parent().parent().find('.item-amount').val()
  val2 = $(elm).parent().parent().parent().parent().find('.item-price').val()
  # console.log val1, val2, val1 * val2
  $(elm).parent().parent().parent().parent().find('.item-sum').val val1 * val2
  @calculateTotalSum()
@chungwa.receiptForm.calculateTotalSum = ->
  $('.template .item-sum').val(0)
  sum = 0
  $('.item-sum').each ->
    sum += parseInt( $(this).val() )
  $('.total-sum').text('$' + sum)


@chungwa.receiptForm.validateForm = (formElm)->
  # hide those errors
  $("[id^=error_]").hide()

  validateForm = ->
    hasError = false

    elm = $('#error_payment_type_id')
    if $('#receipt_payment_type_text').val().length == 0
      hasError = true
      elm.show()
    else
      elm.hide()
      if $("#items").children().length == 0
        hasError = true

    return hasError



  $(formElm).find('input').change ->
    validateForm()

  $(formElm).submit ->
    hasError = validateForm()
    return false if hasError
    return true
