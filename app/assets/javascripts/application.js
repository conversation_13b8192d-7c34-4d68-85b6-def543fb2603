// This is a manifest file that'll be compiled into application.js, which will include all the files
// listed below.
//
// Any JavaScript/Coffee file within this directory, lib/assets/javascripts, vendor/assets/javascripts,
// or vendor/assets/javascripts of plugins, if any, can be referenced here using a relative path.
//
// It's not advisable to add code directly here, but if you do, it'll appear at the bottom of the
// the compiled file.
//
// WARNING: THE FIRST BLANK LINE MARKS THE END OF WHAT'S TO BE PROCESSED, ANY BLANK LINE SHOULD
// GO AFTER THE REQUIRES BELOW.
//
//------------------------------ require turbolinks
//= require active_admin
//= require jquery
//= require jquery_ujs
//= require jquery-ui-1.9.2
//= require foundation
//= require moment
//= require moment_zhtw
//= require_tree .


$(function() {
  // TODO: rembmer client-side expenses changes.

  $('#receipt_item_split_expense').bind('change keyup', function() {
    var expense = parseFloat($(this).val());
    $('#receipt_item_split_turnover').val( parseFloat($('#receipt_item_price').val()) - expense );

    $('#split_item_expense').text( $('#receipt_item_split_amount').val() * expense );

    var turnover = $('#receipt_item_split_turnover').val();
    $('#split_item_turnover').text( $('#receipt_item_split_amount').val() * turnover );



    $.post("/split/expense", {expense:expense}, function(res){console.log(res)});
  });

  // hide the amount indicator if it is only 1.
  if( $('#split_receipt_total_amount').text()*1 === 1) {
  	$('#split_item_amount_indicator').hide()
  }


  $('#receipt_item_split_amount').bind('change keyup', function(){
  	var max = $('#split_item_amount_remain').text() * 1;

  	if ( $(this).val()*1 > max) {
  		$(this).val(max);
  	}

  	$('#split_item_amount_remain_temp').text( $('#split_item_amount_remain').text() - $(this).val() )


  	$('.split_item_amount').text($(this).val());

  	var expense = $('#receipt_item_split_expense').val();
  	$('#split_item_expense').text( $(this).val() * expense );

  	var turnover = $('#receipt_item_split_turnover').val();
  	$('#split_item_turnover').text( $(this).val() * turnover );

  });

});