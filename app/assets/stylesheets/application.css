/*
 * This is a manifest file that'll be compiled into application.css, which will include all the files
 * listed below.
 *
 * Any CSS and SCSS file within this directory, lib/assets/stylesheets, vendor/assets/stylesheets,
 * or vendor/assets/stylesheets of plugins, if any, can be referenced here using a relative path.
 *
 * You're free to add application-wide styles to this file and they'll appear at the top of the
 * compiled file, but it's generally better to create a new file per style scope.
 *
 *= require_self
 *= require general_foundicons
 *= require jquery-ui-theme-base
 *= require_tree .
 *= stub "active_admin"
 */


body.test-env {
  border: 6px solid red;
}
.testing {
  display: none;
}
.test-env .testing {
  background: red;
  color: white;
  padding: 2em;
  font-weight: bold;
  position: fixed;
  top: 0;
  left: 0;
  display: inline;
}

.ultra-important {
  font-size: 2em;
  font-weight: bold;
  margin: .3em;
  color: #c40003;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.8);
}

.green {
  color: #119515;
}
.blue {
  color: #115f95;
}
.light-blue {
  color: #007e95;
}
.red {
  color: #ac1414;
}
.orange {
  color: #d77b19;
}


.search form .row {
  padding: 0;
}

.student-header .student-label {
  border: 1px solid orange;
  padding: 5px 8px;
}
.student-header .student-label:hover {
  border-color: red;
}

.important-information {
  color: #db2f18;
}

.wrapper.columns {
  border-radius: 3px;
  background: #FFFFD2;
  border: 1px solid #bbb;
  box-shadow: 0 1px 2px #aaa;
  /*max-width: 1000px;*/
  min-height: 800px;
  margin-bottom: 1em;
}

.wrapper .back-header {
  background: rgba( 255, 255, 0, .1);
  border-bottom: 1px solid #fff;
  padding-bottom: 1.5em;
  margin-bottom: 2em;
}
.wrapper .back-header:hover {
  background: rgba(255, 255, 0, .2);
}

.wrapper .row:hover {
//  background: ;
}

.wrapper h1 {
  font-size: 2.2em;
}

#side-nav li {
  text-overflow: clip;
  white-space: nowrap;
  overflow: hidden;
}

#main-area, #side-nav {
  -webkit-transition: width .3s ease-out;
}

.info-table, table.info-table tr:nth-child(n) {
  background: none;
  border: none;
}
.info-table {
//  border: 1px solid #ffa763;
}
.info-table th, .info-table td {
  padding-top: 1.2rem;
  padding-bottom: 1.2rem;
  background: none;
}
.info-table th {
  font-size: 1.7rem;
  font-weight: normal;
  padding-right: 1rem;
  text-align: right;
  width: 100px;
}
#summaryTab .info-table td {
  font-weight: bold;
  font-size: 2rem;
  text-align: left;
}
.exams-table {
  border: 1px solid #ffa753;
}
.exams-table th {
  background: #ffa753;
  font-weight: normal;
}
.info-licenses-table {
  border: 1px solid #989ce2;
}
.info-licenses-table th {
  background: #989ce2;
  font-weight: normal;
}
.info-receipts-table {
  border: 1px solid #7dcf7f;
}
.info-receipts-table th {
  background: #7dcf7f;
  font-weight: normal;
}
#summaryTab th, #summaryTab td {
  text-align: center;
}
#summaryTab td {
  font-size: 1.2em;
}
.normal-weight {
  font-weight: normal !important;
}
#summaryTab .remarks li{
  font-size: 1.0rem;
  line-height: 1.5em;
}


.small.remarks {
  font-size: 0.1rem;
}

#student_search input {
  height: 50px;
}
#student_search .button {
  padding: 0;
}

/* radio input */
input[type='radio']:checked + label {
  font-weight: bold;
}

.radio-groups span {
  clear: both;
  overflow: auto;
  display: block;
  margin: 1em 0;
}
.radio-groups input, .radio-groups label {
  float: left;
  padding-left: 5px;
}

/* Major layout */

.full.row {
  width: 1300px;
  max-width: 100%;
}

.radio, .input.boolean {
  width: 30%;
}

.field_with_errors .radio, .field_with_errors .input.boolean {
    width: 100%;
}

table td .input.boolean {
  width: 100%;
}

.center {
  text-align: center;
}

.icon {
  width: 16px;
  -webkit-transition: all .3s ease-out;
}
.icon:hover {
//  -webkit-filter: grayscale(1);
//  filter: grayscale(1);
  -webkit-transform: scale(1.4);
  transform: scale(1.4);
}

/* Foundation Icon File */
@font-face {
  font-family: '[set]Foundicons';
  src: url('fonts/[set]_foundicons.eot');
  src: url('fonts/[set]_foundicons.eot?#iefix') format('embedded-opentype'),
       url('fonts/[set]_foundicons.woff') format('woff'),
       url('fonts/[set]_foundicons.ttf') format('truetype'),
       url('fonts/[set]_foundicons.svg#[set]Foundicons') format('svg');
  font-weight: normal;
  font-style: normal;
}

[class*="foundicon-"] {
  display: inline;
  width: auto;
  height: auto;
  line-height: inherit;
  vertical-align: baseline;
  background-image: none;
  background-position: 0 0;
  background-repeat: repeat;
}

[class*="foundicon-"]:before {
  font-family: "[set]Foundicons";
  font-weight: normal;
  font-style: normal;
  text-decoration: inherit;
}

/*************/

input[readonly='readonly'] {
  background-color: #DDDDDD !important;
}

.circle {
  border-radius: 50%;
}


.row small {
  font-size: .8em;
  color: gray;
}


.header {
  padding-top: 1em;
}



form .row {
  padding: 0.8em 0;
}

form .row:hover {
  background: rgba(255, 255, 255, .4);
}

form .radio {
  padding: 0.5em 0;
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
form .radio:hover {
  background: #fff7cc;
}


.important.alert-box {
  background: #FFF700;
  color: #555;
  text-shadow: none;
}



.tabs.vertical > li {
  position: relative;
}

.tabs.vertical li ul { margin-left: 0; padding-top: 10px; padding-bottom: 10px; border-left: solid 3px #eee; border-right: solid 1px #eee; margin-left: -4px; -webkit-box-shadow: 1px 0 0 white inset; -moz-box-shadow: 1px 0 0 white inset; box-shadow: 1px 0 0 white inset; }
.tabs.vertical li ul li { border: none; }
.tabs.vertical li ul li a { cursor: pointer !important; font-weight: normal; color: #666 !important; padding: 7px 0 7px 20px; background: #fff !important; }
.tabs.vertical li ul li a:hover { color: #111 !important; }
.tabs.vertical li ul li.active { border: none; }
.tabs.vertical li ul li.active a { font-weight: bold; color: #222 !important; }


/* Heading left table */
table.left-heading thead{
  float: left;
}
table.left-heading tbody {
  float: left;
}
table.left-heading th, table.left-heading td {
  display: block;
}


span.secondary {
  color: gray;
}


form select, form input[type="checkbox"] { width: auto; } /* revert the select:100% style */

input[type="text"].important-field {
  background-image: none;
  background-color: #FFF700;
  color: black;
}
input[type="text"].alert {
  background-color: red;
}
input[type="text"].important-field:focus {
  background-color: #FFFCA7;
}


.delete-link {
    color: red;
}


.income-split-pane {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 320px;
  background: #efefef;
  background: rgba(255, 255, 255, .95);
  border-top: 2px solid orange;
  overflow-y: scroll;
}
.income-split-pane .row.full {
  padding-bottom: 30px;
}