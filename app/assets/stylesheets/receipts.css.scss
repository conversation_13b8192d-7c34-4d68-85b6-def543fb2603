// Place all the styles related to the Receipts controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/

@media all {
  table.receipt-print {
    tr, td {
      background-color: transparent !important;
    }

    h1 {
      font-size: 1.5em !important;
      text-align: center !important;
    }
  }
}

form {
  input[type="radio"] {
    margin-right: 0.5em;
  }
  input[type="radio"]:checked {
    outline: 3px solid blue;
  }
  
  // details summary {
  //   padding: 0.5em 0;
  // }
  
  // details label {
  //   display: inline;
  // }
  // details li {
  //   list-style: none;
  //   margin-left: 1em;
  // }

  #receipt_payment_type_text {
    font-size: 2em;
    height: 2em;
  }

  .payment_type_text_input {
    position: sticky;
    top: 0;

    z-index: 999;

    label {
      background: #FFFED7;
      font-weight: bold;
    }
    input {
      font-weight: bold;
    }
  }

  ul.payment_types {
    list-style: none;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    grid-gap: 3px;

    .payment_type_card {
      border: 3px solid lightblue;
      padding: 1em;
      border-radius: 5px;
      position: relative;
      display: flex;
      align-items: center;

      &:hover {
        background: snow;
      }

      input {
        position: relative;
        z-index: 1;
      }

      .text-image {
        display: inline-block;
        width: 30px;
        height: 30px;
        background: lightblue;
        padding: 5px;
        border-radius: 5px;
        text-align: center;
        font-weight: bold;
        position: relative;
        z-index: 1;
      }

      .text-value {
        padding: 1em 3em 1em 0.5em;
        position: relative;
        z-index: 1;
      }

      :checked ~ .text-value {
        font-weight: bold;
      }

      :checked ~ .background-shade {
        position: absolute;
        background: yellow;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 0;

      }

      input {
        zoom: 2;
      }

      img {
        width: 30px;
        height: 30px;
        object-fit: contain;
        position: relative;
        z-index: 1;
      }
    }
  }
}

